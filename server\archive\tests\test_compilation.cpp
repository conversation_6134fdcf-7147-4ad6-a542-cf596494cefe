// 简单的编译测试文件
#include "src/GameEngine/GameEngine.h"
#include "src/GameEngine/StorageManager.h"
#include "src/GameEngine/TradeManager.h"
#include "src/GameEngine/QuestManager.h"
#include "src/GameEngine/MiniMapManager.h"
#include <iostream>

using namespace MirServer;

int main() {
    std::cout << "=== GameEngine Core Features Compilation Test ===" << std::endl;
    
    try {
        // 测试GameEngine实例化
        auto& engine = GameEngine::GetInstance();
        std::cout << "✓ GameEngine instance created successfully" << std::endl;
        
        // 测试管理器访问
        auto* storageManager = engine.GetStorageManager();
        auto* tradeManager = engine.GetTradeManager();
        auto* questManager = engine.GetQuestManager();
        auto* miniMapManager = engine.GetMiniMapManager();
        
        if (storageManager) {
            std::cout << "✓ StorageManager accessible" << std::endl;
        }
        
        if (tradeManager) {
            std::cout << "✓ TradeManager accessible" << std::endl;
        }
        
        if (questManager) {
            std::cout << "✓ QuestManager accessible" << std::endl;
        }
        
        if (miniMapManager) {
            std::cout << "✓ MiniMapManager accessible" << std::endl;
        }
        
        std::cout << "=== All Core Features Compiled Successfully ===" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
