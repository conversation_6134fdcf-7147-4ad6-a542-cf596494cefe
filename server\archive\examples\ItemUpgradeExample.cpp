// ItemUpgradeExample.cpp - 物品强化系统使用示例
#include "ItemManager.h"
#include "../Common/Logger.h"
#include <iostream>
#include <vector>

namespace MirServer {

class ItemUpgradeExample {
public:
    static void RunExample() {
        Logger::Info("=== 物品强化系统使用示例 ===");
        
        // 初始化物品管理器
        ItemManager itemManager;
        if (!itemManager.Initialize("./data")) {
            Logger::Error("Failed to initialize ItemManager");
            return;
        }
        
        // 示例1: 创建武器并强化
        DemonstrateWeaponUpgrade(itemManager);
        
        // 示例2: 使用不同材料强化
        DemonstrateMaterialUpgrade(itemManager);
        
        // 示例3: 强化失败处理
        DemonstrateUpgradeFailure(itemManager);
        
        // 示例4: 配置强化参数
        DemonstrateConfigurationChange(itemManager);
        
        Logger::Info("=== 示例演示完成 ===");
    }

private:
    static void DemonstrateWeaponUpgrade(ItemManager& itemManager) {
        Logger::Info("\n--- 示例1: 武器强化 ---");
        
        // 创建一把测试剑
        UserItem sword;
        sword.itemIndex = 1;
        sword.itemName = "新手剑";
        sword.dura = 100;
        sword.duraMax = 100;
        memset(sword.btValue, 0, sizeof(sword.btValue));
        sword.value.FromBytes(sword.btValue);
        
        Logger::Info("创建武器: " + sword.itemName);
        Logger::Info("初始等级: +" + std::to_string(itemManager.GetItemUpgradeLevel(sword)));
        
        // 尝试强化到+3
        for (int targetLevel = 1; targetLevel <= 3; targetLevel++) {
            Logger::Info("\n尝试强化到 +" + std::to_string(targetLevel));
            
            // 创建基础材料（黑铁矿石）
            std::vector<UserItem> materials;
            UserItem blackIron;
            blackIron.itemIndex = 100;
            blackIron.dura = 3;  // 纯度3
            materials.push_back(blackIron);
            
            // 计算成功率和费用
            int successRate = itemManager.CalculateUpgradeSuccessRate(sword, materials);
            DWORD cost = itemManager.CalculateUpgradeCost(sword);
            
            Logger::Info("成功率: " + std::to_string(successRate) + "%");
            Logger::Info("费用: " + std::to_string(cost) + " 金币");
            
            // 执行强化
            DWORD actualCost = 0;
            auto result = itemManager.UpgradeItemWithMaterials(sword, materials, actualCost);
            
            switch (result) {
                case ItemManager::UpgradeResult::SUCCESS:
                    Logger::Info("强化成功! 当前等级: +" + std::to_string(itemManager.GetItemUpgradeLevel(sword)));
                    Logger::Info("新名称: " + sword.itemName);
                    break;
                case ItemManager::UpgradeResult::FAILED:
                    Logger::Info("强化失败，但物品保留");
                    break;
                case ItemManager::UpgradeResult::ITEM_DESTROYED:
                    Logger::Info("强化失败，物品被摧毁!");
                    return;
                default:
                    Logger::Info("强化出现其他问题");
                    break;
            }
        }
    }
    
    static void DemonstrateMaterialUpgrade(ItemManager& itemManager) {
        Logger::Info("\n--- 示例2: 使用高级材料强化 ---");
        
        // 创建一把+5的武器
        UserItem weapon;
        weapon.itemIndex = 2;
        weapon.itemName = "精良剑";
        weapon.dura = 100;
        weapon.duraMax = 100;
        memset(weapon.btValue, 0, sizeof(weapon.btValue));
        itemManager.SetItemUpgradeLevel(weapon, 5);
        itemManager.UpdateItemName(weapon);
        
        Logger::Info("当前武器: " + weapon.itemName);
        
        // 使用高级材料组合
        std::vector<UserItem> materials;
        
        // 添加黑铁矿石
        UserItem blackIron;
        blackIron.itemIndex = 101;  // 纯度1黑铁
        blackIron.dura = 5;
        materials.push_back(blackIron);
        
        // 添加祝福油
        UserItem blessingOil;
        blessingOil.itemIndex = 120;
        blessingOil.dura = 1;
        materials.push_back(blessingOil);
        
        // 添加灵魂宝石
        UserItem soulGem;
        soulGem.itemIndex = 121;
        soulGem.dura = 1;
        materials.push_back(soulGem);
        
        Logger::Info("使用材料:");
        Logger::Info("- 纯度1黑铁矿石 x5");
        Logger::Info("- 祝福油 x1");
        Logger::Info("- 灵魂宝石 x1");
        
        // 计算成功率
        int successRate = itemManager.CalculateUpgradeSuccessRate(weapon, materials);
        DWORD cost = itemManager.CalculateUpgradeCost(weapon);
        
        Logger::Info("总成功率: " + std::to_string(successRate) + "%");
        Logger::Info("强化费用: " + std::to_string(cost) + " 金币");
        
        // 执行强化
        DWORD actualCost = 0;
        auto result = itemManager.UpgradeItemWithMaterials(weapon, materials, actualCost);
        
        switch (result) {
            case ItemManager::UpgradeResult::SUCCESS:
                Logger::Info("高级材料强化成功! 当前等级: +" + std::to_string(itemManager.GetItemUpgradeLevel(weapon)));
                break;
            case ItemManager::UpgradeResult::FAILED:
                Logger::Info("强化失败，但物品保留");
                break;
            case ItemManager::UpgradeResult::ITEM_DESTROYED:
                Logger::Info("强化失败，物品被摧毁!");
                break;
            default:
                Logger::Info("强化出现其他问题");
                break;
        }
    }
    
    static void DemonstrateUpgradeFailure(ItemManager& itemManager) {
        Logger::Info("\n--- 示例3: 强化失败处理 ---");
        
        // 创建一把高等级武器
        UserItem weapon;
        weapon.itemIndex = 3;
        weapon.itemName = "传说剑";
        weapon.dura = 100;
        weapon.duraMax = 100;
        memset(weapon.btValue, 0, sizeof(weapon.btValue));
        itemManager.SetItemUpgradeLevel(weapon, 8);  // 设置为+8
        itemManager.UpdateItemName(weapon);
        
        Logger::Info("高等级武器: " + weapon.itemName);
        Logger::Info("当前等级: +" + std::to_string(itemManager.GetItemUpgradeLevel(weapon)));
        
        // 在高等级时强化风险很大
        std::vector<UserItem> materials;
        UserItem blackIron;
        blackIron.itemIndex = 100;
        blackIron.dura = 1;
        materials.push_back(blackIron);
        
        int successRate = itemManager.CalculateUpgradeSuccessRate(weapon, materials);
        Logger::Info("成功率: " + std::to_string(successRate) + "%");
        Logger::Info("警告: 高等级强化失败可能导致物品被摧毁!");
        
        // 模拟多次强化尝试的统计
        int successCount = 0;
        int failCount = 0;
        int destroyCount = 0;
        const int attempts = 100;
        
        for (int i = 0; i < attempts; i++) {
            UserItem testWeapon = weapon;  // 复制武器进行测试
            DWORD cost = 0;
            auto result = itemManager.UpgradeItemWithMaterials(testWeapon, materials, cost);
            
            switch (result) {
                case ItemManager::UpgradeResult::SUCCESS:
                    successCount++;
                    break;
                case ItemManager::UpgradeResult::FAILED:
                    failCount++;
                    break;
                case ItemManager::UpgradeResult::ITEM_DESTROYED:
                    destroyCount++;
                    break;
                default:
                    break;
            }
        }
        
        Logger::Info("\n" + std::to_string(attempts) + "次强化尝试统计:");
        Logger::Info("成功: " + std::to_string(successCount) + " 次 (" + 
                    std::to_string((double)successCount/attempts*100) + "%)");
        Logger::Info("失败: " + std::to_string(failCount) + " 次 (" + 
                    std::to_string((double)failCount/attempts*100) + "%)");
        Logger::Info("摧毁: " + std::to_string(destroyCount) + " 次 (" + 
                    std::to_string((double)destroyCount/attempts*100) + "%)");
    }
    
    static void DemonstrateConfigurationChange(ItemManager& itemManager) {
        Logger::Info("\n--- 示例4: 自定义强化配置 ---");
        
        // 获取当前配置
        const auto& currentConfig = itemManager.GetUpgradeConfig();
        Logger::Info("当前配置:");
        Logger::Info("- 最大强化等级: " + std::to_string(currentConfig.maxUpgradeLevel));
        Logger::Info("- 基础成功率: " + std::to_string(currentConfig.baseSuccessRate) + "%");
        Logger::Info("- 基础费用: " + std::to_string(currentConfig.baseCost));
        Logger::Info("- 可摧毁物品: " + std::string(currentConfig.canDestroy ? "是" : "否"));
        
        // 设置新的配置（更宽松的设置）
        ItemManager::UpgradeConfig newConfig;
        newConfig.maxUpgradeLevel = 15;      // 最大15级
        newConfig.baseSuccessRate = 95;      // 95%基础成功率
        newConfig.successRateDecrement = 5;  // 每级减少5%
        newConfig.minSuccessRate = 10;       // 最低10%成功率
        newConfig.baseCost = 500;            // 更低的基础费用
        newConfig.costMultiplier = 1.3f;     // 更低的费用倍数
        newConfig.canDestroy = false;        // 不会摧毁物品
        
        itemManager.SetUpgradeConfig(newConfig);
        
        Logger::Info("\n应用新配置:");
        Logger::Info("- 最大强化等级: " + std::to_string(newConfig.maxUpgradeLevel));
        Logger::Info("- 基础成功率: " + std::to_string(newConfig.baseSuccessRate) + "%");
        Logger::Info("- 基础费用: " + std::to_string(newConfig.baseCost));
        Logger::Info("- 可摧毁物品: " + std::string(newConfig.canDestroy ? "是" : "否"));
        
        // 测试新配置下的强化
        UserItem weapon;
        weapon.itemIndex = 4;
        weapon.itemName = "测试武器";
        weapon.dura = 100;
        weapon.duraMax = 100;
        memset(weapon.btValue, 0, sizeof(weapon.btValue));
        itemManager.SetItemUpgradeLevel(weapon, 10);
        
        std::vector<UserItem> materials;
        UserItem material;
        material.itemIndex = 100;
        material.dura = 1;
        materials.push_back(material);
        
        int successRate = itemManager.CalculateUpgradeSuccessRate(weapon, materials);
        DWORD cost = itemManager.CalculateUpgradeCost(weapon);
        
        Logger::Info("\n+10武器在新配置下:");
        Logger::Info("- 成功率: " + std::to_string(successRate) + "%");
        Logger::Info("- 费用: " + std::to_string(cost) + " 金币");
        Logger::Info("- 失败时不会被摧毁");
    }
};

} // namespace MirServer

int main() {
    try {
        MirServer::ItemUpgradeExample::RunExample();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Example failed: " << e.what() << std::endl;
        return 1;
    }
}
