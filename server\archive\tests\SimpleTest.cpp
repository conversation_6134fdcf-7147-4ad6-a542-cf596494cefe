// 简单的GameEngine组件测试
#include "Environment.h"
#include "StorageManager.h"
#include "TradeManager.h"
#include "QuestManager.h"
#include "MiniMapManager.h"
#include "RepairManager.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>

using namespace MirServer;

// 测试Environment独立功能
bool TestEnvironmentStandalone() {
    Logger::Info("Testing Environment standalone...");
    
    try {
        Environment env("TestMap", 100, 100);
        
        // 测试基本设置
        env.SetMapDesc("Test Map Description");
        env.SetMainMapName("MainMap");
        env.SetSubMapName("SubMap");
        env.SetServerIndex(1);
        env.SetRequestLevel(10);
        env.SetMinMap(1);
        env.SetMainMap(true);
        
        // 验证设置
        if (env.GetMapDesc() != "Test Map Description") {
            Logger::Error("Map description test failed");
            return false;
        }
        
        if (env.GetMainMapName() != "MainMap") {
            Logger::Error("Main map name test failed");
            return false;
        }
        
        if (env.GetServerIndex() != 1) {
            Logger::Error("Server index test failed");
            return false;
        }
        
        // 测试地图标志
        MapFlags flags;
        flags.isSafe = true;
        flags.isFightZone = false;
        flags.isDark = true;
        flags.hasMusic = true;
        flags.musicID = 123;
        flags.expRate = true;
        flags.expRateValue = 200;
        
        env.SetMapFlags(flags);
        
        Point testPos(50, 50);
        if (!env.IsSafeZone(testPos)) {
            Logger::Error("Safe zone test failed");
            return false;
        }
        
        if (env.IsFightZone(testPos)) {
            Logger::Error("Fight zone test failed");
            return false;
        }
        
        if (!env.IsDarknessZone(testPos)) {
            Logger::Error("Darkness zone test failed");
            return false;
        }
        
        // 测试环境信息
        std::string envInfo = env.GetEnvironmentInfo();
        if (envInfo.empty()) {
            Logger::Error("Environment info test failed");
            return false;
        }
        
        Logger::Info("Environment standalone test passed!");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("Exception in TestEnvironmentStandalone: " + std::string(e.what()));
        return false;
    }
}

// 测试管理器创建
bool TestManagerCreation() {
    Logger::Info("Testing manager creation...");
    
    try {
        // 测试创建各个管理器
        auto storageManager = std::make_unique<StorageManager>();
        auto tradeManager = std::make_unique<TradeManager>();
        auto questManager = std::make_unique<QuestManager>();
        auto miniMapManager = std::make_unique<MiniMapManager>();
        auto repairManager = std::make_unique<RepairManager>();
        
        if (!storageManager || !tradeManager || !questManager || 
            !miniMapManager || !repairManager) {
            Logger::Error("Failed to create managers");
            return false;
        }
        
        Logger::Info("Manager creation test passed!");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("Exception in TestManagerCreation: " + std::string(e.what()));
        return false;
    }
}

// 测试基本数据结构
bool TestDataStructures() {
    Logger::Info("Testing data structures...");
    
    try {
        // 测试Point结构
        Point p1(10, 20);
        Point p2(10, 20);
        Point p3(30, 40);
        
        if (!(p1 == p2)) {
            Logger::Error("Point equality test failed");
            return false;
        }
        
        if (p1 == p3) {
            Logger::Error("Point inequality test failed");
            return false;
        }
        
        // 测试UserItem结构
        UserItem item;
        item.wIndex = 1;
        item.nDura = 100;
        item.nDuraMax = 100;
        
        if (item.wIndex != 1 || item.nDura != 100) {
            Logger::Error("UserItem test failed");
            return false;
        }
        
        // 测试MapFlags结构
        MapFlags flags;
        flags.isSafe = true;
        flags.isFightZone = false;
        flags.expRateValue = 150;
        
        if (!flags.isSafe || flags.isFightZone || flags.expRateValue != 150) {
            Logger::Error("MapFlags test failed");
            return false;
        }
        
        Logger::Info("Data structures test passed!");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("Exception in TestDataStructures: " + std::string(e.what()));
        return false;
    }
}

// 主测试函数
int main() {
    Logger::Info("=== Simple GameEngine Component Tests ===");
    
    int totalTests = 0;
    int passedTests = 0;
    
    // 运行测试
    totalTests++;
    if (TestEnvironmentStandalone()) {
        passedTests++;
    }
    
    totalTests++;
    if (TestManagerCreation()) {
        passedTests++;
    }
    
    totalTests++;
    if (TestDataStructures()) {
        passedTests++;
    }
    
    // 打印结果
    Logger::Info("=== Test Results ===");
    Logger::Info("Total Tests: " + std::to_string(totalTests));
    Logger::Info("Passed Tests: " + std::to_string(passedTests));
    Logger::Info("Failed Tests: " + std::to_string(totalTests - passedTests));
    
    double successRate = totalTests > 0 ? (double)passedTests / totalTests * 100.0 : 0.0;
    Logger::Info("Success Rate: " + std::to_string(successRate) + "%");
    
    if (passedTests == totalTests) {
        Logger::Info("✓ All tests passed!");
        return 0;
    } else {
        Logger::Error("✗ Some tests failed!");
        return 1;
    }
}
