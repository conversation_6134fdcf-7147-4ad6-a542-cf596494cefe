#include "../src/GameEngine/GuildManager.h"
#include "../src/Common/Logger.h"
#include <iostream>
#include <memory>

using namespace MirServer;

// 简化的PlayObject实现用于演示
class DemoPlayer : public PlayObject {
public:
    DemoPlayer(const std::string& name, int gold = 50000)
        : m_charName(name), m_gold(gold) {
        m_humDataInfo.guildName = "";
        m_humDataInfo.guildRank = 0;
    }

    const std::string& GetCharName() const override { return m_charName; }
    const std::string& GetGuildName() const override { return m_humDataInfo.guildName; }
    BYTE GetGuildRank() const override { return m_humDataInfo.guildRank; }
    int GetGold() const override { return m_gold; }
    void DecGold(int amount) override { m_gold = std::max(0, m_gold - amount); }
    const HumDataInfo& GetHumDataInfo() const override { return m_humDataInfo; }

    void SendDefMessage(WORD msg, WORD param1, WORD param2, WORD param3, WORD param4) override {
        std::cout << "[" << m_charName << "] 收到系统消息: " << msg << std::endl;
    }

    void SendMessage(const std::string& message, int color) override {
        std::cout << "[" << m_charName << "] " << message << std::endl;
    }

private:
    std::string m_charName;
    int m_gold;
    HumDataInfo m_humDataInfo;
};

void DemoGuildCreationAndManagement() {
    std::cout << "\n=== 行会创建和管理演示 ===" << std::endl;

    // 初始化行会管理器
    auto& guildManager = GuildManager::GetInstance();
    guildManager.Initialize();

    // 创建玩家
    auto chief = std::make_unique<DemoPlayer>("龙城霸主", 100000);
    auto member1 = std::make_unique<DemoPlayer>("剑客无名", 30000);
    auto member2 = std::make_unique<DemoPlayer>("法师传说", 25000);

    std::cout << "\n1. 创建行会..." << std::endl;
    bool result = guildManager.CreateGuild("龙城行会", chief.get());
    if (result) {
        std::cout << "✓ 行会 '龙城行会' 创建成功！" << std::endl;
    }

    Guild* guild = guildManager.FindGuild("龙城行会");
    if (!guild) {
        std::cout << "✗ 找不到行会！" << std::endl;
        return;
    }

    std::cout << "\n2. 添加成员..." << std::endl;
    guild->AddMember(member1.get(), GuildRank::MEMBER);
    guild->AddMember(member2.get(), GuildRank::MEMBER);

    std::cout << "行会成员数量: " << guild->GetMemberCount() << std::endl;

    std::cout << "\n3. 提升成员职位..." << std::endl;
    guild->UpdateMemberRank("剑客无名", GuildRank::CAPTAIN, "队长");
    guild->UpdateMemberRank("法师传说", GuildRank::VICE_CHIEF, "副会长");

    std::cout << "剑客无名 职位: " << guild->GetRankName("剑客无名") << std::endl;
    std::cout << "法师传说 职位: " << guild->GetRankName("法师传说") << std::endl;
}

void DemoGuildDonationSystem() {
    std::cout << "\n=== 行会捐献系统演示 ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    Guild* guild = guildManager.FindGuild("龙城行会");
    if (!guild) return;

    auto donor = std::make_unique<DemoPlayer>("富豪玩家", 200000);
    guild->AddMember(donor.get(), GuildRank::MEMBER);

    std::cout << "\n1. 金币捐献..." << std::endl;
    std::cout << "捐献前玩家金币: " << donor->GetGold() << std::endl;
    std::cout << "捐献前行会金币: " << guild->GetGuildGold() << std::endl;

    guild->DonateGold(donor.get(), 50000);

    std::cout << "捐献后玩家金币: " << donor->GetGold() << std::endl;
    std::cout << "捐献后行会金币: " << guild->GetGuildGold() << std::endl;
    std::cout << "玩家总捐献: " << guild->GetTotalDonation("富豪玩家") << std::endl;

    std::cout << "\n2. 物品捐献..." << std::endl;
    guild->DonateItem(donor.get(), "祝福油", 20);
    guild->DonateItem(donor.get(), "金创药", 100);
}

void DemoGuildLevelingSystem() {
    std::cout << "\n=== 行会升级系统演示 ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    Guild* guild = guildManager.FindGuild("龙城行会");
    if (!guild) return;

    std::cout << "当前行会等级: " << guild->GetGuildLevel() << std::endl;
    std::cout << "当前行会经验: " << guild->GetGuildExp() << std::endl;
    std::cout << "升级所需经验: " << guild->GetRequiredExpForNextLevel() << std::endl;

    std::cout << "\n添加经验..." << std::endl;
    guild->AddGuildExp(1500);

    std::cout << "升级后行会等级: " << guild->GetGuildLevel() << std::endl;
    std::cout << "升级后行会经验: " << guild->GetGuildExp() << std::endl;
    std::cout << "下次升级所需经验: " << guild->GetRequiredExpForNextLevel() << std::endl;
}

void DemoGuildSkillSystem() {
    std::cout << "\n=== 行会技能系统演示 ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    Guild* guild = guildManager.FindGuild("龙城行会");
    if (!guild) return;

    // 确保有足够金币
    guild->SetGuildGold(100000);

    std::cout << "可学习的技能:" << std::endl;
    auto skills = guild->GetAvailableSkills();
    for (size_t i = 0; i < skills.size(); ++i) {
        std::cout << (i + 1) << ". " << skills[i] << std::endl;
    }

    std::cout << "\n学习技能..." << std::endl;
    guild->LearnGuildSkill("行会攻击力提升");
    guild->LearnGuildSkill("行会防御力提升");

    std::cout << "行会攻击力提升 等级: " << guild->GetGuildSkillLevel("行会攻击力提升") << std::endl;

    std::cout << "\n升级技能..." << std::endl;
    guild->UpgradeGuildSkill("行会攻击力提升");
    guild->UpgradeGuildSkill("行会攻击力提升");

    std::cout << "升级后 行会攻击力提升 等级: " << guild->GetGuildSkillLevel("行会攻击力提升") << std::endl;
}

void DemoGuildWarehouseSystem() {
    std::cout << "\n=== 行会仓库系统演示 ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    Guild* guild = guildManager.FindGuild("龙城行会");
    if (!guild) return;

    auto captain = std::make_unique<DemoPlayer>("仓库管理员");
    guild->AddMember(captain.get(), GuildRank::CAPTAIN);

    std::cout << "\n存入物品..." << std::endl;
    guild->DepositItem(captain.get(), "屠龙刀", 1);
    guild->DepositItem(captain.get(), "倚天剑", 1);
    guild->DepositItem(captain.get(), "金币", 10000);

    std::cout << "\n仓库物品列表:" << std::endl;
    auto items = guild->GetWarehouseItems();
    for (const auto& item : items) {
        std::cout << "- " << item << std::endl;
    }

    std::cout << "\n取出物品..." << std::endl;
    guild->WithdrawItem(captain.get(), "金币", 5000);

    std::cout << "\n更新后的仓库物品列表:" << std::endl;
    items = guild->GetWarehouseItems();
    for (const auto& item : items) {
        std::cout << "- " << item << std::endl;
    }
}

void DemoGuildRankingSystem() {
    std::cout << "\n=== 行会排名系统演示 ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();

    // 创建更多行会用于排名演示
    auto chief2 = std::make_unique<DemoPlayer>("霸王", 80000);
    auto chief3 = std::make_unique<DemoPlayer>("剑圣", 60000);

    guildManager.CreateGuild("霸王行会", chief2.get());
    guildManager.CreateGuild("剑圣行会", chief3.get());

    // 为不同行会设置不同属性
    Guild* guild1 = guildManager.FindGuild("龙城行会");
    Guild* guild2 = guildManager.FindGuild("霸王行会");
    Guild* guild3 = guildManager.FindGuild("剑圣行会");

    if (guild2) {
        guild2->SetBuildPoint(500);
        guild2->SetAurae(300);
    }
    if (guild3) {
        guild3->SetBuildPoint(200);
        guild3->SetAurae(150);
    }

    std::cout << "\n更新排名..." << std::endl;
    guildManager.UpdateAllGuildRankings();

    std::cout << "\n行会排行榜 (前3名):" << std::endl;
    auto rankings = guildManager.GetGuildRankings(3);
    for (size_t i = 0; i < rankings.size(); ++i) {
        Guild* guild = rankings[i];
        std::cout << (i + 1) << ". " << guild->GetGuildName()
                  << " (等级: " << guild->GetGuildLevel()
                  << ", 成员: " << guild->GetMemberCount()
                  << ", 分数: " << guild->CalculateRankingScore() << ")" << std::endl;
    }

    std::cout << "\n统计信息:" << std::endl;
    std::cout << "总行会数量: " << guildManager.GetTotalGuildCount() << std::endl;
    std::cout << "总成员数量: " << guildManager.GetTotalMemberCount() << std::endl;

    Guild* topGuild = guildManager.GetTopGuild();
    if (topGuild) {
        std::cout << "排名第一的行会: " << topGuild->GetGuildName() << std::endl;
    }
}

void DemoGuildConfiguration() {
    std::cout << "\n=== 行会配置系统演示 ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    Guild* guild = guildManager.FindGuild("龙城行会");
    if (!guild) return;

    std::cout << "\n设置行会配置..." << std::endl;

    // 设置权限配置
    guild->SetConfigBool("Permissions.AllowMemberInvite", true);
    guild->SetConfigBool("Permissions.RequireApproval", false);

    // 设置战争配置
    guild->SetConfigInt("War.DefaultDuration", 7200000); // 2小时
    guild->SetConfigBool("War.AutoAcceptWar", true);

    // 设置仓库配置
    guild->SetConfigInt("Warehouse.MaxItems", 2000);
    guild->SetConfigInt("Warehouse.AccessLevel", 1); // 队长以上可访问

    // 设置捐献配置
    guild->SetConfigInt("Donation.MinGoldAmount", 5000);
    guild->SetConfigString("Donation.ExpRewardRate", "0.02");

    std::cout << "✓ 行会配置已设置" << std::endl;

    std::cout << "\n读取行会配置..." << std::endl;

    bool allowInvite = guild->GetConfigBool("Permissions.AllowMemberInvite");
    int warDuration = guild->GetConfigInt("War.DefaultDuration");
    int maxItems = guild->GetConfigInt("Warehouse.MaxItems");
    std::string expRate = guild->GetConfigString("Donation.ExpRewardRate");

    std::cout << "允许成员邀请: " << (allowInvite ? "是" : "否") << std::endl;
    std::cout << "战争持续时间: " << warDuration / 1000 / 60 << " 分钟" << std::endl;
    std::cout << "仓库最大物品数: " << maxItems << std::endl;
    std::cout << "经验奖励比率: " << expRate << std::endl;

    std::cout << "\n测试默认配置..." << std::endl;

    // 测试不存在的配置项，应该返回默认值
    bool nonExistent = guild->GetConfigBool("NonExistent.Setting", true);
    int defaultValue = guild->GetConfigInt("NonExistent.Number", 999);

    std::cout << "不存在的布尔配置(默认true): " << (nonExistent ? "是" : "否") << std::endl;
    std::cout << "不存在的数字配置(默认999): " << defaultValue << std::endl;
}

void DemoGuildFileOperations() {
    std::cout << "\n=== 行会文件操作演示 ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    Guild* guild = guildManager.FindGuild("龙城行会");
    if (!guild) return;

    std::cout << "\n保存行会数据..." << std::endl;
    guild->SaveToFile();
    std::cout << "✓ 行会数据已保存到文件" << std::endl;
    std::cout << "✓ 行会配置已保存到 .ini 文件" << std::endl;

    std::cout << "\n保存行会列表..." << std::endl;
    guildManager.SaveGuildList();
    std::cout << "✓ 行会列表已保存" << std::endl;

    std::cout << "\n文件说明:" << std::endl;
    std::cout << "- 行会数据文件: GuildBase/" << guild->GetGuildName() << ".txt" << std::endl;
    std::cout << "- 行会配置文件: GuildBase/" << guild->GetGuildName() << ".ini" << std::endl;
    std::cout << "- 行会列表文件: GuildList.txt" << std::endl;
}

int main() {
    std::cout << "=== 传奇私服行会系统完整演示 ===" << std::endl;

    try {
        DemoGuildCreationAndManagement();
        DemoGuildDonationSystem();
        DemoGuildLevelingSystem();
        DemoGuildSkillSystem();
        DemoGuildWarehouseSystem();
        DemoGuildRankingSystem();
        DemoGuildConfiguration();
        DemoGuildFileOperations();

        std::cout << "\n=== 演示完成！所有功能运行正常 ===" << std::endl;
        return 0;
    }
    catch (const std::exception& e) {
        std::cout << "\n=== 演示出错: " << e.what() << " ===" << std::endl;
        return 1;
    }
}
