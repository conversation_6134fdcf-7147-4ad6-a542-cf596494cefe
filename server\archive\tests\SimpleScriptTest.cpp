#include <iostream>
#include <string>
#include <vector>
#include <cassert>

// 简化版本的脚本解析器测试，不依赖其他模块

class SimpleScriptParser {
public:
    static std::string Trim(const std::string& str) {
        size_t start = str.find_first_not_of(" \t\r\n");
        if (start == std::string::npos) {
            return "";
        }
        size_t end = str.find_last_not_of(" \t\r\n");
        return str.substr(start, end - start + 1);
    }
    
    static std::vector<std::string> SplitParams(const std::string& str) {
        std::vector<std::string> result;
        std::string current;
        bool inQuotes = false;
        
        for (size_t i = 0; i < str.length(); i++) {
            char c = str[i];
            
            if (c == '"' || c == '\'') {
                inQuotes = !inQuotes;
            } else if (c == ' ' && !inQuotes) {
                if (!current.empty()) {
                    result.push_back(Trim(current));
                    current.clear();
                }
            } else {
                current += c;
            }
        }
        
        if (!current.empty()) {
            result.push_back(Trim(current));
        }
        
        return result;
    }
    
    static bool IsLabel(const std::string& line) {
        std::string trimmed = Trim(line);
        return trimmed.length() > 2 && trimmed[0] == '[' && trimmed[1] == '@' && 
               trimmed.back() == ']';
    }
    
    static bool IsConditionSection(const std::string& line) {
        std::string trimmed = Trim(line);
        return trimmed == "#IF" || trimmed == "#if";
    }
    
    static bool IsActionSection(const std::string& line) {
        std::string trimmed = Trim(line);
        return trimmed == "#ACT" || trimmed == "#act";
    }
    
    static bool IsElseActionSection(const std::string& line) {
        std::string trimmed = Trim(line);
        return trimmed == "#ELSEACT" || trimmed == "#elseact";
    }
    
    static bool IsOption(const std::string& line) {
        std::string trimmed = Trim(line);
        return trimmed.length() > 0 && trimmed[0] == '<' && 
               trimmed.find('>') != std::string::npos;
    }
    
    static bool IsComment(const std::string& line) {
        std::string trimmed = Trim(line);
        return trimmed.length() > 0 && (trimmed[0] == ';' || trimmed[0] == '/' || 
               (trimmed.length() > 1 && trimmed[0] == '/' && trimmed[1] == '/'));
    }
    
    static std::string ExtractLabel(const std::string& line) {
        std::string trimmed = Trim(line);
        if (trimmed.length() > 3 && trimmed[0] == '[' && trimmed[1] == '@' && 
            trimmed.back() == ']') {
            return trimmed.substr(2, trimmed.length() - 3);
        }
        return "";
    }
    
    static std::string ExtractOptionText(const std::string& line) {
        std::string trimmed = Trim(line);
        size_t start = trimmed.find('<');
        size_t end = trimmed.find('>', start);
        
        if (start != std::string::npos && end != std::string::npos && end > start) {
            std::string optionPart = trimmed.substr(start + 1, end - start - 1);
            size_t slashPos = optionPart.find('/');
            if (slashPos != std::string::npos) {
                return Trim(optionPart.substr(0, slashPos));
            }
            return Trim(optionPart);
        }
        
        return "";
    }
    
    static std::string ExtractOptionGoto(const std::string& line) {
        std::string trimmed = Trim(line);
        size_t start = trimmed.find('<');
        size_t end = trimmed.find('>', start);
        
        if (start != std::string::npos && end != std::string::npos && end > start) {
            std::string optionPart = trimmed.substr(start + 1, end - start - 1);
            size_t slashPos = optionPart.find('/');
            if (slashPos != std::string::npos) {
                std::string gotoLabel = Trim(optionPart.substr(slashPos + 1));
                // 移除@前缀（如果有）
                if (!gotoLabel.empty() && gotoLabel[0] == '@') {
                    gotoLabel = gotoLabel.substr(1);
                }
                return gotoLabel;
            }
        }
        
        return "";
    }
};

void TestBasicParsing() {
    std::cout << "测试基础解析功能..." << std::endl;
    
    // 测试Trim
    assert(SimpleScriptParser::Trim("  hello  ") == "hello");
    assert(SimpleScriptParser::Trim("") == "");
    assert(SimpleScriptParser::Trim("   ") == "");
    std::cout << "✓ Trim测试通过" << std::endl;
    
    // 测试SplitParams
    auto params = SimpleScriptParser::SplitParams("CHECKLEVEL > 10");
    assert(params.size() == 3);
    assert(params[0] == "CHECKLEVEL");
    assert(params[1] == ">");
    assert(params[2] == "10");
    std::cout << "✓ SplitParams测试通过" << std::endl;
    
    // 测试IsLabel
    assert(SimpleScriptParser::IsLabel("[@main]") == true);
    assert(SimpleScriptParser::IsLabel("[@shop]") == true);
    assert(SimpleScriptParser::IsLabel("[main]") == false);
    assert(SimpleScriptParser::IsLabel("main") == false);
    std::cout << "✓ IsLabel测试通过" << std::endl;
    
    // 测试IsConditionSection
    assert(SimpleScriptParser::IsConditionSection("#IF") == true);
    assert(SimpleScriptParser::IsConditionSection("#if") == true);
    assert(SimpleScriptParser::IsConditionSection("IF") == false);
    std::cout << "✓ IsConditionSection测试通过" << std::endl;
    
    // 测试IsActionSection
    assert(SimpleScriptParser::IsActionSection("#ACT") == true);
    assert(SimpleScriptParser::IsActionSection("#act") == true);
    assert(SimpleScriptParser::IsActionSection("ACT") == false);
    std::cout << "✓ IsActionSection测试通过" << std::endl;
    
    // 测试IsElseActionSection
    assert(SimpleScriptParser::IsElseActionSection("#ELSEACT") == true);
    assert(SimpleScriptParser::IsElseActionSection("#elseact") == true);
    assert(SimpleScriptParser::IsElseActionSection("ELSEACT") == false);
    std::cout << "✓ IsElseActionSection测试通过" << std::endl;
    
    // 测试IsOption
    assert(SimpleScriptParser::IsOption("<购买物品/@shop>") == true);
    assert(SimpleScriptParser::IsOption("<离开/@exit>") == true);
    assert(SimpleScriptParser::IsOption("购买物品") == false);
    std::cout << "✓ IsOption测试通过" << std::endl;
    
    // 测试IsComment
    assert(SimpleScriptParser::IsComment("; 这是注释") == true);
    assert(SimpleScriptParser::IsComment("// 这是注释") == true);
    assert(SimpleScriptParser::IsComment("/ 这不是注释") == false);
    std::cout << "✓ IsComment测试通过" << std::endl;
    
    // 测试ExtractLabel
    assert(SimpleScriptParser::ExtractLabel("[@main]") == "main");
    assert(SimpleScriptParser::ExtractLabel("[@shop]") == "shop");
    assert(SimpleScriptParser::ExtractLabel("[main]") == "");
    std::cout << "✓ ExtractLabel测试通过" << std::endl;
    
    // 测试ExtractOptionText
    assert(SimpleScriptParser::ExtractOptionText("<购买物品/@shop>") == "购买物品");
    assert(SimpleScriptParser::ExtractOptionText("<离开/@exit>") == "离开");
    std::cout << "✓ ExtractOptionText测试通过" << std::endl;
    
    // 测试ExtractOptionGoto
    assert(SimpleScriptParser::ExtractOptionGoto("<购买物品/@shop>") == "shop");
    assert(SimpleScriptParser::ExtractOptionGoto("<离开/@exit>") == "exit");
    std::cout << "✓ ExtractOptionGoto测试通过" << std::endl;
}

void TestScriptFormat() {
    std::cout << "\n测试脚本格式识别..." << std::endl;
    
    std::vector<std::string> testLines = {
        "[@main]",
        "你好，<$USERNAME>！\\",
        "我是<$NPCNAME>。\\",
        "<购买物品/@shop> 我要购买物品\\",
        "<离开/@exit> 离开\\",
        "",
        "[@shop]",
        "#IF",
        "CHECKLEVEL > 10",
        "#ACT",
        "OPENMERCHANT",
        "SENDMSG 5 欢迎光临！",
        "#ELSEACT",
        "SENDMSG 5 等级不够！",
        "",
        "; 这是注释",
        "// 这也是注释"
    };
    
    for (const auto& line : testLines) {
        std::cout << "测试行: \"" << line << "\"" << std::endl;
        
        if (SimpleScriptParser::IsLabel(line)) {
            std::cout << "  -> 标签: " << SimpleScriptParser::ExtractLabel(line) << std::endl;
        } else if (SimpleScriptParser::IsConditionSection(line)) {
            std::cout << "  -> 条件段" << std::endl;
        } else if (SimpleScriptParser::IsActionSection(line)) {
            std::cout << "  -> 动作段" << std::endl;
        } else if (SimpleScriptParser::IsElseActionSection(line)) {
            std::cout << "  -> ELSE动作段" << std::endl;
        } else if (SimpleScriptParser::IsOption(line)) {
            std::cout << "  -> 选项: " << SimpleScriptParser::ExtractOptionText(line) 
                      << " -> " << SimpleScriptParser::ExtractOptionGoto(line) << std::endl;
        } else if (SimpleScriptParser::IsComment(line)) {
            std::cout << "  -> 注释" << std::endl;
        } else if (line.empty()) {
            std::cout << "  -> 空行" << std::endl;
        } else {
            std::cout << "  -> 普通文本" << std::endl;
        }
    }
}

int main() {
    try {
        TestBasicParsing();
        TestScriptFormat();
        
        std::cout << "\n🎉 所有脚本解析器基础测试通过！" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试失败: " << e.what() << std::endl;
        return 1;
    }
}
