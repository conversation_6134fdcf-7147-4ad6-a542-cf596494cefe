// ItemUpgradeTest.cpp - 物品强化系统测试
#include "ItemManager.h"
#include "../Common/Logger.h"
#include <iostream>
#include <cassert>

namespace MirServer {

class ItemUpgradeTest {
public:
    static void RunAllTests() {
        Logger::Info("Starting Item Upgrade System Tests...");
        
        TestBasicUpgrade();
        TestUpgradeWithMaterials();
        TestUpgradeFailure();
        TestUpgradeDestroy();
        TestUpgradeConfiguration();
        TestMaterialBonus();
        
        Logger::Info("All Item Upgrade System Tests Passed!");
    }

private:
    static void TestBasicUpgrade() {
        Logger::Info("Testing basic upgrade functionality...");
        
        // 创建测试物品管理器
        ItemManager itemManager;
        itemManager.Initialize("./data");
        
        // 创建测试武器
        UserItem weapon;
        weapon.itemIndex = 1;  // 假设1是武器ID
        weapon.itemName = "测试剑";
        weapon.dura = 100;
        weapon.duraMax = 100;
        memset(weapon.btValue, 0, sizeof(weapon.btValue));
        weapon.value.FromBytes(weapon.btValue);
        
        // 测试是否可以强化
        bool canUpgrade = itemManager.CanUpgradeItem(weapon);
        Logger::Info("Can upgrade weapon: " + std::string(canUpgrade ? "Yes" : "No"));
        
        // 测试获取强化等级
        int level = itemManager.GetItemUpgradeLevel(weapon);
        assert(level == 0);
        Logger::Info("Initial upgrade level: " + std::to_string(level));
        
        // 测试设置强化等级
        itemManager.SetItemUpgradeLevel(weapon, 3);
        level = itemManager.GetItemUpgradeLevel(weapon);
        assert(level == 3);
        Logger::Info("After setting level to 3: " + std::to_string(level));
        
        Logger::Info("Basic upgrade test passed!");
    }
    
    static void TestUpgradeWithMaterials() {
        Logger::Info("Testing upgrade with materials...");
        
        ItemManager itemManager;
        itemManager.Initialize("./data");
        
        // 创建测试武器
        UserItem weapon;
        weapon.itemIndex = 1;
        weapon.itemName = "测试剑";
        weapon.dura = 100;
        weapon.duraMax = 100;
        memset(weapon.btValue, 0, sizeof(weapon.btValue));
        weapon.value.FromBytes(weapon.btValue);
        
        // 创建强化材料
        std::vector<UserItem> materials;
        
        UserItem blackIron;
        blackIron.itemIndex = 100;  // 黑铁矿石
        blackIron.dura = 5;  // 纯度5
        materials.push_back(blackIron);
        
        UserItem blessingOil;
        blessingOil.itemIndex = 120;  // 祝福油
        blessingOil.dura = 1;
        materials.push_back(blessingOil);
        
        // 测试材料验证
        bool validMaterials = itemManager.ValidateUpgradeMaterials(materials);
        Logger::Info("Materials valid: " + std::string(validMaterials ? "Yes" : "No"));
        
        // 测试成功率计算
        int successRate = itemManager.CalculateUpgradeSuccessRate(weapon, materials);
        Logger::Info("Success rate: " + std::to_string(successRate) + "%");
        
        // 测试费用计算
        DWORD cost = itemManager.CalculateUpgradeCost(weapon);
        Logger::Info("Upgrade cost: " + std::to_string(cost));
        
        Logger::Info("Upgrade with materials test passed!");
    }
    
    static void TestUpgradeFailure() {
        Logger::Info("Testing upgrade failure scenarios...");
        
        ItemManager itemManager;
        itemManager.Initialize("./data");
        
        // 创建高等级武器
        UserItem weapon;
        weapon.itemIndex = 1;
        weapon.itemName = "测试剑";
        weapon.dura = 100;
        weapon.duraMax = 100;
        memset(weapon.btValue, 0, sizeof(weapon.btValue));
        weapon.btValue[13] = 9;  // 设置为9级
        weapon.value.FromBytes(weapon.btValue);
        
        // 测试是否会在失败时摧毁
        bool shouldDestroy = itemManager.ShouldDestroyOnFailure(9);
        Logger::Info("Should destroy at level 9: " + std::string(shouldDestroy ? "Yes" : "No"));
        
        // 测试应用失败效果
        UserItem testWeapon = weapon;
        itemManager.ApplyUpgradeFailure(testWeapon, false);
        Logger::Info("After failure (not destroyed), durability: " + std::to_string(testWeapon.dura));
        
        testWeapon = weapon;
        itemManager.ApplyUpgradeFailure(testWeapon, true);
        Logger::Info("After failure (destroyed), item index: " + std::to_string(testWeapon.itemIndex));
        
        Logger::Info("Upgrade failure test passed!");
    }
    
    static void TestUpgradeDestroy() {
        Logger::Info("Testing upgrade destroy mechanics...");
        
        ItemManager itemManager;
        itemManager.Initialize("./data");
        
        // 测试不同等级的摧毁概率
        for (int level = 0; level <= 10; level++) {
            int destroyCount = 0;
            const int testRuns = 1000;
            
            for (int i = 0; i < testRuns; i++) {
                if (itemManager.ShouldDestroyOnFailure(level)) {
                    destroyCount++;
                }
            }
            
            double destroyRate = (double)destroyCount / testRuns * 100.0;
            Logger::Info("Level " + std::to_string(level) + " destroy rate: " + 
                        std::to_string(destroyRate) + "%");
        }
        
        Logger::Info("Upgrade destroy test passed!");
    }
    
    static void TestUpgradeConfiguration() {
        Logger::Info("Testing upgrade configuration...");
        
        ItemManager itemManager;
        itemManager.Initialize("./data");
        
        // 获取默认配置
        const auto& config = itemManager.GetUpgradeConfig();
        Logger::Info("Default max level: " + std::to_string(config.maxUpgradeLevel));
        Logger::Info("Default base success rate: " + std::to_string(config.baseSuccessRate));
        Logger::Info("Default base cost: " + std::to_string(config.baseCost));
        
        // 测试自定义配置
        ItemManager::UpgradeConfig customConfig;
        customConfig.maxUpgradeLevel = 15;
        customConfig.baseSuccessRate = 90;
        customConfig.baseCost = 2000;
        customConfig.canDestroy = false;
        
        itemManager.SetUpgradeConfig(customConfig);
        
        const auto& newConfig = itemManager.GetUpgradeConfig();
        assert(newConfig.maxUpgradeLevel == 15);
        assert(newConfig.baseSuccessRate == 90);
        assert(newConfig.baseCost == 2000);
        assert(newConfig.canDestroy == false);
        
        Logger::Info("Upgrade configuration test passed!");
    }
    
    static void TestMaterialBonus() {
        Logger::Info("Testing material bonus calculation...");
        
        ItemManager itemManager;
        itemManager.Initialize("./data");
        
        // 测试不同材料的加成
        std::vector<std::pair<ItemManager::UpgradeMaterial, int>> testMaterials = {
            {ItemManager::UpgradeMaterial::BLACK_IRON, 5},
            {ItemManager::UpgradeMaterial::SILVER_ORE, 3},
            {ItemManager::UpgradeMaterial::GOLD_ORE, 2},
            {ItemManager::UpgradeMaterial::DIAMOND, 1},
            {ItemManager::UpgradeMaterial::BLESSING_OIL, 1},
            {ItemManager::UpgradeMaterial::SOUL_GEM, 1},
            {ItemManager::UpgradeMaterial::MEMORY_HELMET, 1}
        };
        
        for (const auto& material : testMaterials) {
            int bonus = itemManager.GetMaterialBonus(material.first, material.second);
            Logger::Info("Material " + std::to_string(static_cast<int>(material.first)) + 
                        " x" + std::to_string(material.second) + " bonus: " + std::to_string(bonus) + "%");
        }
        
        Logger::Info("Material bonus test passed!");
    }
};

} // namespace MirServer

// 主测试函数
int main() {
    try {
        MirServer::ItemUpgradeTest::RunAllTests();
        std::cout << "All tests completed successfully!" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
}
