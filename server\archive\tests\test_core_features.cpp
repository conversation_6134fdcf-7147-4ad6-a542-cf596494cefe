// GameEngine单元测试
#include "GameEngine.h"
#include "StorageManager.h"
#include "TradeManager.h"
#include "QuestManager.h"
#include "MiniMapManager.h"
#include "RepairManager.h"
#include "Environment.h"
#include "MapManager.h"
#include "ItemManager.h"
#include "MagicManager.h"
#include "NPCManager.h"
#include "MonsterManager.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>
#include <cassert>
#include <chrono>
#include <vector>
#include <thread>

using namespace MirServer;

// 测试结果统计
struct TestResults {
    int totalTests = 0;
    int passedTests = 0;
    int failedTests = 0;
    std::vector<std::string> failedTestNames;

    void AddTest(const std::string& testName, bool passed) {
        totalTests++;
        if (passed) {
            passedTests++;
            Logger::Info("✓ " + testName + " - PASSED");
        } else {
            failedTests++;
            failedTestNames.push_back(testName);
            Logger::Error("✗ " + testName + " - FAILED");
        }
    }

    void PrintSummary() {
        Logger::Info("=== Test Summary ===");
        Logger::Info("Total Tests: " + std::to_string(totalTests));
        Logger::Info("Passed: " + std::to_string(passedTests));
        Logger::Info("Failed: " + std::to_string(failedTests));

        if (failedTests > 0) {
            Logger::Error("Failed Tests:");
            for (const auto& testName : failedTestNames) {
                Logger::Error("  - " + testName);
            }
        }

        double successRate = totalTests > 0 ? (double)passedTests / totalTests * 100.0 : 0.0;
        Logger::Info("Success Rate: " + std::to_string(successRate) + "%");
    }
};

// 创建测试玩家
std::unique_ptr<PlayObject> CreateTestPlayer(const std::string& name, int level = 10) {
    auto player = std::make_unique<PlayObject>();
    player->SetCharName(name);
    player->SetLevel(level);
    player->SetJob(JobType::WARRIOR);
    player->IncGold(10000); // 给予10000金币
    return player;
}

// 测试GameEngine初始化
bool TestGameEngineInitialization() {
    try {
        auto& engine = GameEngine::GetInstance();

        // 测试初始化
        bool initialized = engine.Initialize();
        if (!initialized) {
            Logger::Error("GameEngine initialization failed");
            return false;
        }

        // 测试状态
        if (engine.GetState() != GameEngineState::STOPPED) {
            Logger::Error("GameEngine state should be STOPPED after initialization");
            return false;
        }

        // 测试管理器是否创建
        if (!engine.GetUserEngine() || !engine.GetMapManager() ||
            !engine.GetItemManager() || !engine.GetMagicManager() ||
            !engine.GetNPCManager() || !engine.GetMonsterManager() ||
            !engine.GetStorageManager() || !engine.GetTradeManager() ||
            !engine.GetQuestManager() || !engine.GetMiniMapManager() ||
            !engine.GetRepairManager()) {
            Logger::Error("Some managers were not created properly");
            return false;
        }

        // 测试配置
        const auto& config = engine.GetConfig();
        if (config.serverName.empty() || config.serverPort == 0) {
            Logger::Error("GameEngine configuration is invalid");
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        Logger::Error("Exception in TestGameEngineInitialization: " + std::string(e.what()));
        return false;
    }
}

// 测试Environment功能
bool TestEnvironmentFeatures() {
    try {
        Environment env("TestMap", 100, 100);

        // 测试基本设置
        env.SetMapDesc("Test Map Description");
        env.SetMainMapName("MainMap");
        env.SetSubMapName("SubMap");
        env.SetServerIndex(1);
        env.SetRequestLevel(10);
        env.SetMinMap(1);
        env.SetMainMap(true);

        // 验证设置
        if (env.GetMapDesc() != "Test Map Description" ||
            env.GetMainMapName() != "MainMap" ||
            env.GetSubMapName() != "SubMap" ||
            env.GetServerIndex() != 1 ||
            env.GetRequestLevel() != 10 ||
            env.GetMinMap() != 1 ||
            !env.IsMainMap()) {
            Logger::Error("Environment basic settings failed");
            return false;
        }

        // 测试地图标志
        MapFlags flags;
        flags.isSafe = true;
        flags.isFightZone = false;
        flags.isDark = true;
        flags.hasMusic = true;
        flags.musicID = 123;
        flags.expRate = true;
        flags.expRateValue = 200;

        env.SetMapFlags(flags);

        Point testPos(50, 50);
        if (!env.IsSafeZone(testPos) || env.IsFightZone(testPos) ||
            !env.IsDarknessZone(testPos)) {
            Logger::Error("Environment zone checks failed");
            return false;
        }

        // 测试环境信息
        std::string envInfo = env.GetEnvironmentInfo();
        if (envInfo.empty() || envInfo.find("TestMap") == std::string::npos) {
            Logger::Error("Environment info generation failed");
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        Logger::Error("Exception in TestEnvironmentFeatures: " + std::string(e.what()));
        return false;
    }
}

// 测试StorageManager
bool TestStorageManager() {
    try {
        auto& engine = GameEngine::GetInstance();
        auto* storageManager = engine.GetStorageManager();

        if (!storageManager) {
            Logger::Error("StorageManager not available");
            return false;
        }

        auto player = CreateTestPlayer("StorageTestPlayer");

        // 测试打开仓库
        bool opened = storageManager->OpenStorage(player.get(), "123456");
        if (!opened) {
            Logger::Error("Failed to open storage");
            return false;
        }

        // 测试仓库状态
        if (!storageManager->IsStorageOpen(player.get())) {
            Logger::Error("Storage should be open");
            return false;
        }

        // 测试存储金币
        bool goldStored = storageManager->StoreGold(player.get(), 1000);
        if (!goldStored) {
            Logger::Error("Failed to store gold");
            return false;
        }

        // 测试查询仓库金币
        MirServer::DWORD storageGold = storageManager->GetStorageGold(player.get());
        if (storageGold != 1000) {
            Logger::Error("Storage gold amount incorrect: " + std::to_string(storageGold));
            return false;
        }

        // 测试关闭仓库
        bool closed = storageManager->CloseStorage(player.get());
        if (!closed) {
            Logger::Error("Failed to close storage");
            return false;
        }

        // 测试统计信息
        auto stats = storageManager->GetStatistics();
        if (stats.totalStorages == 0) {
            Logger::Error("Storage statistics not updated");
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        Logger::Error("Exception in TestStorageManager: " + std::string(e.what()));
        return false;
    }
}

// 测试TradeManager
bool TestTradeManager() {
    try {
        auto& engine = GameEngine::GetInstance();
        auto* tradeManager = engine.GetTradeManager();

        if (!tradeManager) {
            Logger::Error("TradeManager not available");
            return false;
        }

        auto player1 = CreateTestPlayer("Trader1");
        auto player2 = CreateTestPlayer("Trader2");

        // 设置玩家位置
        Point pos1 = {100, 100};
        Point pos2 = {101, 101};
        player1->SetCurrentPos(pos1);
        player2->SetCurrentPos(pos2);

        // 测试交易请求
        bool requested = tradeManager->RequestTrade(player1.get(), player2.get());
        if (!requested) {
            Logger::Error("Failed to request trade");
            return false;
        }

        // 测试接受交易
        bool accepted = tradeManager->AcceptTrade(player2.get(), player1->GetCharName());
        if (!accepted) {
            Logger::Error("Failed to accept trade");
            return false;
        }

        // 测试设置交易金币
        bool goldSet = tradeManager->SetTradeGold(player1.get(), 100);
        if (!goldSet) {
            Logger::Error("Failed to set trade gold");
            return false;
        }

        // 测试锁定交易
        bool locked1 = tradeManager->LockTrade(player1.get());
        bool locked2 = tradeManager->LockTrade(player2.get());
        if (!locked1 || !locked2) {
            Logger::Error("Failed to lock trade");
            return false;
        }

        // 测试统计信息
        auto stats = tradeManager->GetStatistics();
        if (stats.activeTrades == 0) {
            Logger::Error("Trade statistics not updated");
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        Logger::Error("Exception in TestTradeManager: " + std::string(e.what()));
        return false;
    }
}

// 测试QuestManager
bool TestQuestManager() {
    try {
        auto& engine = GameEngine::GetInstance();
        auto* questManager = engine.GetQuestManager();

        if (!questManager) {
            Logger::Error("QuestManager not available");
            return false;
        }

        auto player = CreateTestPlayer("QuestTestPlayer");

        // 测试获取可用任务
        auto availableQuests = questManager->GetAvailableQuests(player.get(), "新手导师");

        // 测试统计信息
        auto stats = questManager->GetStatistics();
        if (stats.totalQuests < 0) {
            Logger::Error("Quest statistics invalid");
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        Logger::Error("Exception in TestQuestManager: " + std::string(e.what()));
        return false;
    }
}

// 测试MiniMapManager
bool TestMiniMapManager() {
    try {
        auto& engine = GameEngine::GetInstance();
        auto* miniMapManager = engine.GetMiniMapManager();

        if (!miniMapManager) {
            Logger::Error("MiniMapManager not available");
            return false;
        }

        auto player = CreateTestPlayer("MapTestPlayer");

        // 测试加载小地图数据
        bool loaded = miniMapManager->LoadMiniMapData("TestMap");

        // 测试统计信息
        auto stats = miniMapManager->GetStatistics();
        if (stats.totalMaps < 0) {
            Logger::Error("MiniMap statistics invalid");
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        Logger::Error("Exception in TestMiniMapManager: " + std::string(e.what()));
        return false;
    }
}

// 测试RepairManager
bool TestRepairManager() {
    try {
        auto& engine = GameEngine::GetInstance();
        auto* repairManager = engine.GetRepairManager();

        if (!repairManager) {
            Logger::Error("RepairManager not available");
            return false;
        }

        auto player = CreateTestPlayer("RepairTestPlayer");

        // 测试统计信息
        auto stats = repairManager->GetStatistics();
        if (stats.totalRepairs < 0) {
            Logger::Error("Repair statistics invalid");
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        Logger::Error("Exception in TestRepairManager: " + std::string(e.what()));
        return false;
    }
}

// 主测试函数
int main() {
    Logger::Info("=== GameEngine Unit Tests ===");

    TestResults results;

    try {
        // 运行所有单元测试
        results.AddTest("GameEngine Initialization", TestGameEngineInitialization());
        results.AddTest("Environment Features", TestEnvironmentFeatures());
        results.AddTest("Storage Manager", TestStorageManager());
        results.AddTest("Trade Manager", TestTradeManager());
        results.AddTest("Quest Manager", TestQuestManager());
        results.AddTest("MiniMap Manager", TestMiniMapManager());
        results.AddTest("Repair Manager", TestRepairManager());

        // 打印测试结果
        results.PrintSummary();

        // 清理
        auto& engine = GameEngine::GetInstance();
        engine.Finalize();

        return results.failedTests == 0 ? 0 : 1;

    } catch (const std::exception& e) {
        Logger::Error("Test suite failed with exception: " + std::string(e.what()));
        return 1;
    }
}
