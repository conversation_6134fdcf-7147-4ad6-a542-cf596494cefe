// PlayObject.cpp - 玩家对象实现
// TODO: 实现玩家对象

#include "PlayObject.h"
#include "../Protocol/PacketTypes.h"
#include "../Protocol/MessageConverter.h"
#include "../GameEngine/GameEngine.h"
#include "../GameEngine/StorageManager.h"
#include "../GameEngine/PKManager.h"
#include "../GameEngine/GroupManager.h"
#include "../GameEngine/GuildManager.h"
#include "../Common/Logger.h"
#include <algorithm>

namespace MirServer {

// 常量定义
const int MAXBAGITEMS = 46;           // 背包最大物品数
const int MAXSTORAGEITEMCOUNT = 50;   // 仓库最大物品数

PlayObject::PlayObject() : BaseObject() {
    m_humDataInfo = HumDataInfo();
    m_humDataInfo.bagItems.resize(MAXBAGITEMS);
    m_humDataInfo.storageItems.resize(MAXSTORAGEITEMCOUNT);

    // 初始化新成员变量
    m_job = JobType::WARRIOR;
    m_gender = GenderType::MALE;
    m_connectionId = 0;
    m_lastActiveTime = GetCurrentTime();

    // 初始化角色基本信息
    m_humDataInfo.level = 1;
    m_humDataInfo.isDead = false;

    // 初始化基础属性
    InitializeDefaultAbility(m_humDataInfo.abil, m_humDataInfo.job, m_humDataInfo.level);

    // 设置基类属性
    m_hp = m_humDataInfo.abil.HP;
    m_maxHP = m_humDataInfo.abil.MaxHP;
    m_viewRange = 12; // 玩家视野范围更大

    // 初始化攻击速度
    RecalcHitSpeed();
}

PlayObject::~PlayObject() {
    LogoutGame();
}

void PlayObject::Initialize() {
    // 初始化玩家特有的功能
    m_lastViewUpdateTime = GetCurrentTime();

    // 发送初始化信息给客户端
    LoginGame();
}

void PlayObject::Finalize() {
    // 保存数据
    SaveData();

    // 清理组队信息
    auto& groupManager = GroupManager::GetInstance();
    if (groupManager.IsInGroup(this)) {
        // 通知组队管理器玩家下线
        groupManager.OnPlayerLogout(this);

        // 如果是队长，需要特殊处理
        if (groupManager.IsGroupLeader(this)) {
            auto group = groupManager.GetPlayerGroup(this);
            if (group && group->members.size() > 1) {
                // 转移队长给下一个成员
                for (auto& member : group->members) {
                    if (member && member.get() != this && member->IsOnline()) {
                        groupManager.ChangeLeader(this, member.get());
                        break;
                    }
                }
            }
        }

        // 离开组队
        groupManager.LeaveGroup(this);
    }

    // 清理行会信息
    auto& guildManager = GuildManager::GetInstance();
    auto* guild = guildManager.GetPlayerGuild(GetCharName());
    if (guild) {
        // 通知行会管理器玩家下线
        guild->OnPlayerLogout(this);
    }

    // 清理交易信息
    if (m_dealPartner) {
        CancelDeal();
    }
}

void PlayObject::Run() {
    // 处理客户端消息
    ProcessMessages();

    // 更新视野
    DWORD currentTime = GetCurrentTime();
    if (currentTime - m_lastViewUpdateTime > 500) { // 每500ms更新一次视野
        UpdateViewMap();
        m_lastViewUpdateTime = currentTime;
    }

    // 检查升级
    CheckLevelUp();
}

bool PlayObject::Walk(DirectionType dir) {
    Point nextPos = GetNextPosition(dir);

    // 先转向
    TurnTo(dir);

    // 检查是否可以移动
    if (!CanMove(nextPos)) {
        SendDefMessage(Protocol::SM_MOVEFAIL, 0, m_currentPos.x, m_currentPos.y, 0);
        return false;
    }

    // 执行移动
    Point oldPos = m_currentPos;
    m_currentPos = nextPos;
    OnPositionChanged();

    // 发送移动成功消息
    SendDefMessage(Protocol::SM_WALK, 0, nextPos.x, nextPos.y, static_cast<WORD>(dir));

    return true;
}

void PlayObject::SendMessage(const std::string& msg, BYTE color) {
    if (msg.empty()) return;

    // 添加消息到队列，稍后通过ProcessMessages发送
    Message message;
    message.msgType = Protocol::RM_HEAR;
    message.recog = 0;
    message.param = color;
    message.tag = 0;
    message.series = 0;
    message.data = msg;
    m_messageQueue.push_back(message);

    // 记录到日志
    Logger::Debug("SendMessage to " + GetCharName() + ": " + msg);
}

void PlayObject::SendDefMessage(WORD msgType, WORD recog, WORD param, WORD tag, WORD series) {
    // 添加消息到队列，稍后通过ProcessMessages发送
    Message message;
    message.msgType = msgType;
    message.recog = recog;
    message.param = param;
    message.tag = tag;
    message.series = series;
    m_messageQueue.push_back(message);
}

void PlayObject::RecalcAbility() {
    // 基础属性重置
    InitializeDefaultAbility(m_humDataInfo.abil, m_humDataInfo.job, m_humDataInfo.level);

    // 计算装备加成
    for (size_t i = 0; i < static_cast<size_t>(EquipPosition::MAX_EQUIP); i++) {
        const UserItem& item = m_humDataInfo.useItems[i];
        if (item.itemIndex > 0) {
            // TODO: 根据物品属性增加能力值
            // 这里需要物品数据库支持
        }
    }

    // 更新基类属性
    m_hp = std::min(m_hp, m_humDataInfo.abil.MaxHP);
    m_maxHP = m_humDataInfo.abil.MaxHP;

    // 重新计算攻击速度
    RecalcHitSpeed();

    // 通知客户端
    SendAbility();
}

bool PlayObject::TakeOnItem(const UserItem& item, EquipPosition pos) {
    if (pos >= EquipPosition::MAX_EQUIP) return false;

    // 检查是否已有装备
    UserItem& equipSlot = m_humDataInfo.useItems[static_cast<size_t>(pos)];
    if (equipSlot.itemIndex > 0) {
        // 需要先卸下原装备
        return false;
    }

    // 装备物品
    equipSlot = item;

    // 重新计算属性
    RecalcAbility();

    // 通知客户端
    SendDefMessage(Protocol::SM_TAKEONITEM, item.makeIndex, static_cast<WORD>(pos), 0, 0);

    return true;
}

bool PlayObject::TakeOffItem(EquipPosition pos, UserItem& outItem) {
    if (pos >= EquipPosition::MAX_EQUIP) return false;

    UserItem& equipSlot = m_humDataInfo.useItems[static_cast<size_t>(pos)];
    if (equipSlot.itemIndex == 0) {
        return false; // 没有装备
    }

    // 检查背包是否有空间
    if (IsBagFull()) {
        return false;
    }

    // 卸下装备
    outItem = equipSlot;
    equipSlot = UserItem(); // 清空装备槽

    // 添加到背包
    AddBagItem(outItem);

    // 重新计算属性
    RecalcAbility();

    // 通知客户端
    SendDefMessage(Protocol::SM_TAKEOFFITEM, outItem.makeIndex, static_cast<WORD>(pos), 0, 0);

    return true;
}

bool PlayObject::ClearEquipSlot(EquipPosition pos) {
    if (pos >= EquipPosition::MAX_EQUIP) return false;

    UserItem& equipSlot = m_humDataInfo.useItems[static_cast<size_t>(pos)];
    if (equipSlot.itemIndex == 0) {
        return false; // 没有装备
    }

    // 记录装备信息用于通知客户端
    WORD makeIndex = equipSlot.makeIndex;

    // 直接清空装备槽（过期物品消失，不进入背包）
    equipSlot = UserItem();

    // 重新计算属性
    RecalcAbility();

    // 通知客户端装备被移除
    SendDefMessage(Protocol::SM_TAKEOFFITEM, makeIndex, static_cast<WORD>(pos), 0, 0);

    return true;
}

bool PlayObject::AddBagItem(const UserItem& item) {
    if (IsBagFull()) return false;

    m_humDataInfo.bagItems.push_back(item);

    // 通知客户端
    SendDefMessage(Protocol::SM_ADDITEM, item.makeIndex, item.itemIndex, item.dura, 0);

    return true;
}

bool PlayObject::DeleteBagItem(WORD makeIndex) {
    auto it = std::find_if(m_humDataInfo.bagItems.begin(),
                          m_humDataInfo.bagItems.end(),
                          [makeIndex](const UserItem& item) {
                              return item.makeIndex == makeIndex;
                          });

    if (it != m_humDataInfo.bagItems.end()) {
        m_humDataInfo.bagItems.erase(it);

        // 通知客户端
        SendDefMessage(Protocol::SM_DELITEM, makeIndex, 0, 0, 0);
        return true;
    }

    return false;
}

bool PlayObject::IncGold(DWORD amount) {
    if (m_humDataInfo.gold + amount > m_humDataInfo.goldMax) {
        return false;
    }

    m_humDataInfo.gold += amount;
    SendGoldChanged();
    return true;
}

bool PlayObject::DecGold(DWORD amount) {
    if (m_humDataInfo.gold < amount) {
        return false;
    }

    m_humDataInfo.gold -= amount;
    SendGoldChanged();
    return true;
}

void PlayObject::GainExp(DWORD exp) {
    if (exp == 0) return;

    // 检查是否在组队中，如果是则通过组队管理器分配经验
    auto& groupManager = GroupManager::GetInstance();
    if (groupManager.IsInGroup(this)) {
        // 使用组队经验分享系统
        groupManager.ShareExperience(this, exp);
    } else {
        // 单人获得经验
        GainExpDirect(exp);
    }
}

void PlayObject::GainExpDirect(DWORD exp) {
    if (exp == 0) return;

    // 直接增加经验，不通过组队分享
    m_humDataInfo.abil.Exp += exp;
    SendExpChanged();

    // 检查是否可以升级
    CheckLevelUp();
}

bool PlayObject::CheckLevelUp() {
    bool leveledUp = false;

    while (m_humDataInfo.abil.Exp >= m_humDataInfo.abil.MaxExp &&
           m_humDataInfo.level < MAXLEVEL) {
        // 升级
        m_humDataInfo.abil.Exp -= m_humDataInfo.abil.MaxExp;
        m_humDataInfo.level++;
        m_humDataInfo.abil.Level = m_humDataInfo.level;

        // 重新计算属性
        RecalcAbility();

        leveledUp = true;
    }

    if (leveledUp) {
        SendLevelUp();
    }

    return leveledUp;
}

bool PlayObject::IsAttackTarget(const BaseObject* target) const {
    if (!target || target == this) return false;

    // 如果目标是玩家，使用PK系统判断
    if (target->GetObjectType() == ObjectType::HUMAN) {
        const PlayObject* targetPlayer = static_cast<const PlayObject*>(target);

        // 使用PK管理器判断是否可以攻击
        auto& pkManager = PKManager::GetInstance();
        return pkManager.CanAttack(const_cast<PlayObject*>(this), const_cast<PlayObject*>(targetPlayer));
    }

    // 对于怪物，根据攻击模式判断
    if (target->GetObjectType() == ObjectType::MONSTER) {
        return m_attackMode != AttackMode::PEACE;
    }

    return false;
}

bool PlayObject::IsProperTarget(const BaseObject* target) const {
    return IsAttackTarget(target) && CanSee(target);
}

bool PlayObject::IsProperFriend(const BaseObject* target) const {
    if (!target || target == this) return false;

    if (target->GetObjectType() == ObjectType::HUMAN) {
        const PlayObject* player = static_cast<const PlayObject*>(target);

        // 组队成员是朋友
        if (IsGroupMember(player)) return true;

        // 行会成员是朋友（非敌对状态）
        if (IsGuildMember(player)) return true;

        // 行会联盟成员是朋友
        if (IsGuildAlly(player)) return true;

        // TODO: 夫妻、师徒等关系判断
        // 这些关系需要额外的系统支持，暂时不实现
    }

    return false;
}

void PlayObject::Die() {
    BaseObject::Die();

    // 设置死亡状态
    m_humDataInfo.isDead = true;

    // 通知PK管理器玩家死亡
    auto& pkManager = PKManager::GetInstance();
    pkManager.OnPlayerDeath(this, m_lastAttacker);

    // 掉落物品
    ScatterBagItems(m_lastAttacker);
    DropUseItems(m_lastAttacker);

    // 掉落金币（根据PK值决定）
    if (m_humDataInfo.pkPoint > 0) {
        DWORD dropGold = m_humDataInfo.gold / 10; // 掉落10%金币
        if (dropGold > 0) {
            DropGoldDown(dropGold, true, this, m_lastAttacker);
            m_humDataInfo.gold -= dropGold;
        }
    }

    // 通知客户端
    SendDefMessage(Protocol::SM_NOWDEATH, 0, m_currentPos.x, m_currentPos.y, 0);
}

void PlayObject::Revive() {
    BaseObject::Revive();

    // 设置复活状态
    m_humDataInfo.isDead = false;

    // 恢复部分生命值和魔法值
    m_humDataInfo.abil.HP = m_humDataInfo.abil.MaxHP / 2;
    m_humDataInfo.abil.MP = m_humDataInfo.abil.MaxMP / 2;
    m_hp = m_humDataInfo.abil.HP;

    // 通知客户端
    SendAbility();
    SendDefMessage(Protocol::SM_ALIVE, 0, 0, 0, 0);
}

void PlayObject::ReviveAtHome() {
    // 传送到回城点
    SpaceMove(m_humDataInfo.homeMap, m_humDataInfo.homePos.x, m_humDataInfo.homePos.y);

    // 复活
    Revive();
}

void PlayObject::LoginGame() {
    // 发送登录成功消息
    SendDefMessage(Protocol::SM_LOGON, 0, m_currentPos.x, m_currentPos.y, static_cast<WORD>(m_direction));

    // 发送地图信息
    SendMapInfo();

    // 发送角色属性
    SendAbility();

    // 发送背包物品
    SendBagItems();

    // 发送魔法列表
    SendMagics();

    // 通知组队管理器玩家上线
    auto& groupManager = GroupManager::GetInstance();
    groupManager.OnPlayerLogin(this);

    // 通知行会管理器玩家上线
    auto& guildManager = GuildManager::GetInstance();
    auto* guild = guildManager.GetPlayerGuild(GetCharName());
    if (guild) {
        guild->OnPlayerLogin(this);
    }
}

void PlayObject::SendMapInfo() {
    // 发送地图描述信息
    std::string mapDesc = GetMapName();
    if (mapDesc.empty()) {
        mapDesc = "Unknown";
    }

    // 发送地图描述消息
    SendDefMessage(Protocol::SM_MAPDESCRIPTION, 0, 0, 0, 0);

    // 发送地图名称（作为文本消息）
    SendMessage("当前地图: " + mapDesc, 0);
}

void PlayObject::SendAbility() {
    // 发送角色属性信息
    const auto& abil = m_humDataInfo.abil;

    // 发送基础属性
    SendDefMessage(Protocol::SM_ABILITY, 0,
                  abil.Level, abil.Exp, abil.MaxExp);

    // 发送生命值和魔法值
    SendDefMessage(Protocol::SM_HEALTHSPELLCHANGED, 0,
                  abil.HP, abil.MP, 0);

    // 发送攻击力、防御力等详细属性
    // 这里可以根据需要发送更多详细的属性信息
    Logger::Debug("SendAbility to " + GetCharName() +
                 ": Level=" + std::to_string(abil.Level) +
                 " HP=" + std::to_string(abil.HP) + "/" + std::to_string(abil.MaxHP) +
                 " MP=" + std::to_string(abil.MP) + "/" + std::to_string(abil.MaxMP));
}

void PlayObject::SendBagItems() {
    // 发送背包物品列表
    SendDefMessage(Protocol::SM_BAGITEMS, 0,
                  static_cast<WORD>(m_humDataInfo.bagItems.size()), 0, 0);

    // 发送每个背包物品
    for (const auto& item : m_humDataInfo.bagItems) {
        if (item.itemIndex > 0) {
            SendDefMessage(Protocol::SM_ADDITEM, item.makeIndex,
                          item.itemIndex, item.dura, item.duraMax);
        }
    }

    Logger::Debug("SendBagItems to " + GetCharName() +
                 ": " + std::to_string(m_humDataInfo.bagItems.size()) + " items");
}

void PlayObject::SendMagics() {
    // 发送魔法列表
    SendDefMessage(Protocol::SM_SENDMYMAGIC, 0,
                  static_cast<WORD>(m_humDataInfo.magics.size()), 0, 0);

    // 发送每个魔法
    for (const auto& magic : m_humDataInfo.magics) {
        if (magic.magicId > 0) {
            SendDefMessage(Protocol::SM_ADDMAGIC, magic.magicId,
                          magic.level, magic.curTrain, magic.maxTrain);
        }
    }

    Logger::Debug("SendMagics to " + GetCharName() +
                 ": " + std::to_string(m_humDataInfo.magics.size()) + " magics");
}

void PlayObject::SendStorageItems(const std::vector<UserItem>& items, DWORD gold) {
    // 发送仓库物品列表到客户端

    // 发送仓库金币
    SendDefMessage(Protocol::SM_SAVEITEMLIST, 0,
                  static_cast<WORD>(gold & 0xFFFF),
                  static_cast<WORD>(gold >> 16), 0);

    // 发送每个物品
    for (const auto& item : items) {
        if (item.itemIndex > 0) {
            SendDefMessage(Protocol::SM_ADDITEM, item.makeIndex,
                          item.itemIndex, item.dura, item.duraMax);
        }
    }

    Logger::Debug("SendStorageItems to " + GetCharName() +
                 ": " + std::to_string(items.size()) + " items, " +
                 std::to_string(gold) + " gold");
}

void PlayObject::SendGoldChanged() {
    SendDefMessage(Protocol::SM_GOLDCHANGED, 0,
                  static_cast<WORD>(m_humDataInfo.gold & 0xFFFF),
                  static_cast<WORD>(m_humDataInfo.gold >> 16), 0);
}

void PlayObject::SendExpChanged() {
    // 发送经验值变化
    const auto& abil = m_humDataInfo.abil;
    SendDefMessage(Protocol::SM_ABILITY, 0,
                  abil.Level, abil.Exp, abil.MaxExp);

    Logger::Debug("SendExpChanged to " + GetCharName() +
                 ": Exp=" + std::to_string(abil.Exp) + "/" + std::to_string(abil.MaxExp));
}

void PlayObject::SendLevelUp() {
    // 发送升级效果
    SendDefMessage(Protocol::SM_ABILITY, 0,
                  m_humDataInfo.abil.Level, 0, 0);

    // 发送升级消息
    SendMessage("恭喜！您升到了 " + std::to_string(m_humDataInfo.abil.Level) + " 级！", 6);

    // 发送升级特效（如果需要）
    SendDefMessage(Protocol::SM_FEATURECHANGED, GetFeature(), 0, 0, 0);

    Logger::Info("Player " + GetCharName() + " leveled up to " + std::to_string(m_humDataInfo.abil.Level));
}

void PlayObject::SendHealthChanged() {
    SendDefMessage(Protocol::SM_HEALTHSPELLCHANGED, 0,
                  m_humDataInfo.abil.HP, m_humDataInfo.abil.MP, 0);
}

void PlayObject::SendDelItems() {
    // 发送删除物品列表（清空背包显示）
    SendDefMessage(Protocol::SM_DELITEM, 0, 0, 0, 0);

    Logger::Debug("SendDelItems to " + GetCharName());
}

void PlayObject::SendUseMagic() {
    // 发送使用魔法效果
    SendDefMessage(Protocol::SM_SPELL, 0,
                  m_currentPos.x, m_currentPos.y, static_cast<WORD>(m_direction));

    Logger::Debug("SendUseMagic to " + GetCharName());
}

void PlayObject::SendFeatureChanged() {
    // 发送外观特征变化（如PK值变化导致的名字颜色变化）
    SendDefMessage(Protocol::SM_FEATURECHANGED, GetFeature(), 0, 0, 0);

    Logger::Debug("SendFeatureChanged to " + GetCharName() +
                 ": feature=" + std::to_string(GetFeature()));
}

void PlayObject::Say(const std::string& msg) {
    if (msg.empty()) return;

    // 发送说话消息给自己
    SendMessage(GetCharName() + ": " + msg, 0);

    // 通过SendRefMsg发送给周围玩家
    SendRefMsg(Protocol::RM_HEAR, 0, 0, 0, 0, msg);

    Logger::Debug("Player " + GetCharName() + " says: " + msg);
}

void PlayObject::CancelDeal() {
    if (m_dealPartner) {
        m_dealPartner->SetDealPartner(nullptr);
        m_dealPartner = nullptr;
    }

    m_dealItems.clear();
    m_dealGold = 0;
    m_dealLocked = false;

    SendDefMessage(Protocol::SM_DEALCANCEL, 0, 0, 0, 0);
}

void PlayObject::IncPKPoint(int value) {
    m_humDataInfo.pkPoint += value;
    if (m_humDataInfo.pkPoint < 0) {
        m_humDataInfo.pkPoint = 0;
    }

    // 使用PK管理器更新状态
    auto& pkManager = PKManager::GetInstance();

    // PK值影响名称颜色
    if (pkManager.IsRedName(this)) {
        SetNameColor(12); // 红名
    } else if (pkManager.IsYellowName(this)) {
        SetNameColor(11); // 黄名
    } else {
        SetNameColor(0); // 白名
    }
}

bool PlayObject::IsGroupMember(const PlayObject* player) const {
    if (!player) return false;

    // 使用组队管理器检查是否是组队成员
    auto& groupManager = GroupManager::GetInstance();
    return groupManager.IsGroupMember(const_cast<PlayObject*>(this), const_cast<PlayObject*>(player));
}

bool PlayObject::IsInGroup() const {
    auto& groupManager = GroupManager::GetInstance();
    return groupManager.IsInGroup(const_cast<PlayObject*>(this));
}

bool PlayObject::IsGroupLeader() const {
    auto& groupManager = GroupManager::GetInstance();
    return groupManager.IsGroupLeader(const_cast<PlayObject*>(this));
}

std::vector<std::shared_ptr<PlayObject>> PlayObject::GetGroupMembers() const {
    auto& groupManager = GroupManager::GetInstance();
    return groupManager.GetGroupMembers(const_cast<PlayObject*>(this));
}

void PlayObject::ProcessMessages() {
    // 处理消息队列中的消息
    while (!m_messageQueue.empty()) {
        const Message& msg = m_messageQueue.front();

        // 创建默认消息并发送
        Protocol::DefaultMessage defMsg;
        defMsg.wIdent = msg.msgType;
        defMsg.nRecog = msg.recog;
        defMsg.wParam = msg.param;
        defMsg.wTag = msg.tag;
        defMsg.wSeries = msg.series;

        // 编码并发送消息
        std::string encodedMsg = Protocol::MessageConverter::EncodeMessage(defMsg);
        if (!msg.data.empty()) {
            encodedMsg += Protocol::MessageConverter::EncodeString(msg.data);
        }
        SendPacketToClient(encodedMsg);

        m_messageQueue.pop_front();
    }
}

void PlayObject::UpdateViewMap() {
    // TODO: 更新视野内的对象列表
    GetViewObjects(m_viewList);
}

void PlayObject::OnPositionChanged() {
    // 位置改变时的处理
    UpdateViewMap();
}

void PlayObject::OnDirectionChanged() {
    // 方向改变时的处理
}

void PlayObject::OnStateChanged() {
    // 状态改变时的处理
}

void PlayObject::OnHPChanged() {
    // 生命值改变时的处理
    SendHealthChanged();
}

void PlayObject::SaveData() {
    // TODO: 保存角色数据到数据库
}

void PlayObject::LoadData() {
    // TODO: 从数据库加载角色数据
}

bool PlayObject::UseBagItem(WORD makeIndex) {
    // TODO: 实现使用背包物品
    return false;
}

bool PlayObject::AddMagic(const UserMagic& magic) {
    // TODO: 实现添加魔法
    m_humDataInfo.magics.push_back(magic);
    return true;
}

bool PlayObject::DeleteMagic(WORD magicId) {
    // TODO: 实现删除魔法
    return false;
}

bool PlayObject::UseMagic(WORD magicId, BaseObject* target) {
    // TODO: 实现使用魔法
    return false;
}



void PlayObject::BeAttacked(BaseObject* attacker, int damage) {
    if (!attacker || damage <= 0) {
        return;
    }

    // 记录最后攻击者
    SetLastAttacker(attacker);

    // 扣除生命值
    if (damage >= static_cast<int>(m_hp)) {
        m_hp = 0;
        Die();
    } else {
        m_hp -= damage;
        OnHPChanged();
    }

    // TODO: 实现具体的被攻击逻辑
    // 1. 播放被攻击效果
    // 2. 发送伤害数值
    // 3. 处理反击逻辑
}

const UserItem* PlayObject::GetEquipItem(EquipPosition pos) const {
    if (pos >= EquipPosition::MAX_EQUIP) return nullptr;
    const UserItem& item = m_humDataInfo.useItems[static_cast<size_t>(pos)];
    return (item.itemIndex > 0) ? &item : nullptr;
}

void PlayObject::Whisper(const std::string& targetName, const std::string& msg) {
    if (targetName.empty() || msg.empty()) {
        return;
    }

    // TODO: 通过GameEngine查找目标玩家并发送私聊消息
    // 这需要与GameEngine的玩家管理器集成

    // 暂时发送给自己作为确认
    SendMessage("对 " + targetName + " 说: " + msg, 3);
}

// 重写物品掉落方法
void PlayObject::ScatterBagItems(BaseObject* itemCreator) {
    if (m_humDataInfo.pkPoint <= 0) {
        return; // 白名玩家不掉落背包物品
    }

    // 根据PK值决定掉落数量
    int dropCount = std::min(3, static_cast<int>(m_humDataInfo.pkPoint / 100));

    for (int i = 0; i < dropCount && !m_humDataInfo.bagItems.empty(); i++) {
        // 随机选择一个背包物品掉落
        int randomIndex = rand() % m_humDataInfo.bagItems.size();
        UserItem dropItem = m_humDataInfo.bagItems[randomIndex];

        // 掉落物品
        if (DropItemDown(dropItem, 3, true, this, itemCreator)) {
            // 从背包中移除
            m_humDataInfo.bagItems.erase(m_humDataInfo.bagItems.begin() + randomIndex);
        }
    }
}

void PlayObject::DropUseItems(BaseObject* dropCreator) {
    if (m_humDataInfo.pkPoint < 200) {
        return; // PK值不够高不掉落装备
    }

    // 红名玩家有几率掉落装备
    int dropChance = std::min(50, static_cast<int>(m_humDataInfo.pkPoint / 20)); // 最高50%几率

    for (int i = 0; i < static_cast<int>(EquipPosition::MAX_EQUIP); i++) {
        UserItem& equipItem = m_humDataInfo.useItems[i];
        if (equipItem.itemIndex > 0 && (rand() % 100) < dropChance) {
            // 掉落装备
            if (DropItemDown(equipItem, 2, true, this, dropCreator)) {
                // 清空装备位
                equipItem = UserItem();

                // 重新计算属性
                RecalcAbilitys();

                // 只掉落一件装备
                break;
            }
        }
    }
}

// 技能相关的目标判断方法实现
bool PlayObject::IsProperTargetSKILL_54(const BaseObject* target) const {
    // SKILL_54: 基础攻击技能目标判断
    return IsProperTarget(target);
}

bool PlayObject::IsProperTargetSKILL_55(int level, const BaseObject* target) const {
    // SKILL_55: 等级相关的技能目标判断
    if (!IsProperTarget(target)) return false;

    // 根据技能等级调整判断逻辑
    if (level <= 0) return false;

    // 检查目标等级差距（高等级技能可以攻击更强的目标）
    if (target->GetObjectType() == ObjectType::HUMAN) {
        const PlayObject* targetPlayer = static_cast<const PlayObject*>(target);
        int levelDiff = targetPlayer->GetLevel() - GetLevel();
        return levelDiff <= (level * 10); // 技能等级越高，可攻击的等级差距越大
    }

    return true;
}

bool PlayObject::IsProperTargetSKILL_56(const BaseObject* target, int targetX, int targetY) const {
    // SKILL_56: 位置相关的技能目标判断
    if (!IsProperTarget(target)) return false;

    // 检查目标是否在指定位置附近
    Point targetPos = target->GetCurrentPos();
    int distance = std::max(std::abs(targetPos.x - targetX), std::abs(targetPos.y - targetY));

    return distance <= 2; // 在2格范围内
}

bool PlayObject::IsProperTargetSKILL_57(const BaseObject* target) const {
    // SKILL_57: 特殊技能目标判断
    if (!IsProperTarget(target)) return false;

    // 检查是否是友好目标（治疗类技能可能需要）
    if (IsProperFriend(target)) return true;

    // 检查是否是敌对目标
    return IsAttackTarget(target);
}

bool PlayObject::IsProperTargetSKILL_70(const BaseObject* target) const {
    // SKILL_70: 高级技能目标判断（可能与城堡系统相关）
    if (!IsProperTarget(target)) return false;

    // 检查是否是玩家
    if (target->GetObjectType() != ObjectType::HUMAN) return false;

    const PlayObject* targetPlayer = static_cast<const PlayObject*>(target);

    // 检查是否是城堡主人或行会成员（根据原项目逻辑）
    if (IsGuildMember(targetPlayer)) return false; // 不能攻击同行会成员

    // 检查PK状态
    auto& pkManager = PKManager::GetInstance();
    return pkManager.CanAttack(const_cast<PlayObject*>(this), const_cast<PlayObject*>(targetPlayer));
}

// 技能开关控制方法实现
void PlayObject::ThrustingOnOff(bool enable) {
    m_boUseThrusting = enable;

    // 发送技能状态变化消息给客户端
    SendDefMessage(Protocol::SM_SKILL_SWITCH, 1, enable ? 1 : 0, 0, 0);

    if (enable) {
        SendMessage("刺杀剑法已开启", 2);
    } else {
        SendMessage("刺杀剑法已关闭", 2);
    }
}

void PlayObject::HalfMoonOnOff(bool enable) {
    m_boUseHalfMoon = enable;

    // 发送技能状态变化消息给客户端
    SendDefMessage(Protocol::SM_SKILL_SWITCH, 2, enable ? 1 : 0, 0, 0);

    if (enable) {
        SendMessage("半月弯刀已开启", 2);
    } else {
        SendMessage("半月弯刀已关闭", 2);
    }
}

void PlayObject::SkillCrsOnOff(bool enable) {
    m_boCrsHitkill = enable;

    // 发送技能状态变化消息给客户端
    SendDefMessage(Protocol::SM_SKILL_SWITCH, 3, enable ? 1 : 0, 0, 0);

    if (enable) {
        SendMessage("野蛮冲撞已开启", 2);
    } else {
        SendMessage("野蛮冲撞已关闭", 2);
    }
}

void PlayObject::Skill42OnOff(bool enable) {
    m_bo42kill = enable;

    // 发送技能状态变化消息给客户端
    SendDefMessage(Protocol::SM_SKILL_SWITCH, 42, enable ? 1 : 0, 0, 0);

    if (enable) {
        SendMessage("技能42已开启", 2);
    } else {
        SendMessage("技能42已关闭", 2);
    }
}

void PlayObject::Skill43OnOff(bool enable) {
    m_bo43kill = enable;

    // 发送技能状态变化消息给客户端
    SendDefMessage(Protocol::SM_SKILL_SWITCH, 43, enable ? 1 : 0, 0, 0);

    if (enable) {
        SendMessage("技能43已开启", 2);
    } else {
        SendMessage("技能43已关闭", 2);
    }
}

// 高级战斗方法实现
bool PlayObject::RunTo(BYTE dir, bool flag, int destX, int destY) {
    // 检查是否可以跑步
    if (IsDead() || m_state == ObjectState::PARALYSIS) {
        return false;
    }

    // 检查跑步间隔时间
    DWORD currentTime = GetCurrentTime();
    if (currentTime - m_lastMoveTime < 300) { // 300ms跑步间隔
        return false;
    }

    // 计算目标位置
    Point targetPos = GetNextPosition(static_cast<DirectionType>(dir));

    // 如果指定了目标坐标，检查是否朝向正确
    if (destX >= 0 && destY >= 0) {
        if (targetPos.x != destX || targetPos.y != destY) {
            return false; // 方向不正确
        }
    }

    // 检查是否可以移动到目标位置
    if (!CanMove(targetPos)) {
        return false;
    }

    // 执行跑步移动
    Point oldPos = m_currentPos;
    m_currentPos = targetPos;
    m_direction = static_cast<DirectionType>(dir);
    m_lastMoveTime = currentTime;

    // 通知位置变化
    OnPositionChanged();

    // 发送跑步消息给客户端和周围玩家
    SendDefMessage(Protocol::SM_RUN, 0, targetPos.x, targetPos.y, dir);
    SendRefMsg(Protocol::RM_RUN, dir, targetPos.x, targetPos.y, 0, "");

    return true;
}

bool PlayObject::AllowFireHitSkill() const {
    // 检查是否允许使用烈火剑法
    if (!m_boFireHitSkill) return false;

    // 检查冷却时间（防止连续使用）
    DWORD currentTime = GetCurrentTime();
    if (currentTime - m_dwLatestFireHitTick < 1000) { // 1秒冷却时间
        return false;
    }

    // 检查是否有足够的魔法值
    if (m_humDataInfo.abil.MP < 10) { // 烈火剑法需要10点魔法值
        return false;
    }

    return true;
}

// 辅助战斗方法实现
void PlayObject::AttackNearTargets(int damage) {
    // 半月弯刀攻击周围目标
    std::vector<BaseObject*> nearTargets;

    // 搜索周围1格范围内的目标
    for (int dx = -1; dx <= 1; dx++) {
        for (int dy = -1; dy <= 1; dy++) {
            if (dx == 0 && dy == 0) continue; // 跳过自己的位置

            Point checkPos = {m_currentPos.x + dx, m_currentPos.y + dy};

            // 获取该位置的对象
            std::vector<BaseObject*> objects;
            GetObjectsAtPosition(checkPos, objects);

            for (BaseObject* obj : objects) {
                if (obj && IsAttackTarget(obj)) {
                    nearTargets.push_back(obj);
                }
            }
        }
    }

    // 对所有目标造成伤害
    for (BaseObject* target : nearTargets) {
        target->BeAttacked(this, damage);
    }
}

int PlayObject::CalculateDamage(const BaseObject* target) const {
    if (!target) return 0;

    // 基础攻击力
    int minDC = m_humDataInfo.abil.DC.min;
    int maxDC = m_humDataInfo.abil.DC.max;

    // 随机伤害
    int damage = GenerateRandom(minDC, maxDC);

    // 武器加成
    const UserItem* weapon = GetEquipItem(EquipPosition::WEAPON);
    if (weapon) {
        // TODO: 根据武器属性计算额外伤害
        damage += weapon->dura / 100; // 简化的武器伤害计算
    }

    // 等级加成
    damage += GetLevel() / 10;

    // 确保最小伤害为1
    return std::max(1, damage);
}

void PlayObject::RecalcHitSpeed() {
    // 重新计算攻击速度值，根据原Delphi项目逻辑
    // 注意：这里的m_nHitSpeed是攻击速度值，不是敏捷属性
    // 原Delphi项目中：m_nHitSpeed := 0; （默认值为0，通过装备和技能增加）
    m_nHitSpeed = 0; // 基础攻击速度值为0

    // 武器影响攻击速度
    const UserItem* weapon = GetEquipItem(EquipPosition::WEAPON);
    if (weapon) {
        // TODO: 根据武器属性获取攻击速度加成
        // 在原项目中，武器的攻击速度存储在物品属性中
        // 这里暂时使用简化计算：每1000耐久度增加1点攻击速度
        m_nHitSpeed += weapon->dura / 1000;
    }

    // 其他装备影响攻击速度
    // TODO: 添加其他装备的攻击速度加成
    // 例如：靴子、手镯等可能有攻击速度属性

    // 技能影响攻击速度
    // TODO: 添加技能对攻击速度的影响
    // 例如：某些技能可能提供攻击速度加成

    // 状态影响攻击速度
    // TODO: 添加状态对攻击速度的影响
    // 例如：中毒、麻痹等状态可能降低攻击速度
    // 神圣战甲术等技能可能提高攻击速度
}

DWORD PlayObject::GetAttackSpeed() const {
    // 基础攻击间隔时间（毫秒）- 对应原Delphi项目的g_Config.dwHitIntervalTime
    DWORD baseInterval = 1000;

    // 根据原Delphi项目的计算公式：
    // dwAttackTime := _MAX(0, Integer(g_Config.dwHitIntervalTime) - m_nHitSpeed * g_Config.ClientConf.btItemSpeed)
    // 其中 btItemSpeed 通常为 10
    int itemSpeed = 10;

    // 计算攻击间隔：基础间隔 - (攻击速度值 × 物品速度系数)
    // m_nHitSpeed越高，攻击间隔越短，攻击越快
    int calculatedInterval = static_cast<int>(baseInterval) - m_nHitSpeed * itemSpeed;
    DWORD attackInterval = std::max(0, calculatedInterval);

    // 最小攻击间隔200ms，最大不超过2000ms
    // 当m_nHitSpeed = 0时，攻击间隔为1000ms
    // 当m_nHitSpeed = 5时，攻击间隔为950ms
    // 当m_nHitSpeed = 80时，攻击间隔为200ms（达到最小值）
    return std::max(200U, std::min(2000U, attackInterval));
}

void PlayObject::GetObjectsAtPosition(const Point& pos, std::vector<BaseObject*>& objects) const {
    // 简化实现：通过地图管理器获取指定位置的对象
    // 这里需要与地图系统集成，暂时提供基础实现
    objects.clear();

    // TODO: 实际实现应该通过地图管理器获取指定位置的所有对象
    // 这里提供一个简化的实现，仅作为示例

    // 检查视野内的对象
    for (BaseObject* obj : m_viewList) {
        if (obj && obj->GetCurrentPos().x == pos.x && obj->GetCurrentPos().y == pos.y) {
            objects.push_back(obj);
        }
    }
}

void PlayObject::GroupMsg(const std::string& msg) {
    // 使用组队管理器发送组队消息
    auto& groupManager = GroupManager::GetInstance();
    groupManager.SendGroupMessage(this, msg);
}

void PlayObject::GuildMsg(const std::string& msg) {
    // 使用行会管理器发送行会消息
    auto& guildManager = GuildManager::GetInstance();
    auto* guild = guildManager.GetPlayerGuild(GetCharName());

    if (guild) {
        guild->SendGuildMessage(GetCharName() + ": " + msg);
    } else {
        SendMessage("您不在任何行会中", 0);
    }
}

bool PlayObject::IsGuildMember(const PlayObject* player) const {
    if (!player || m_humDataInfo.guildName.empty() || player->GetGuildName().empty()) {
        return false;
    }

    return m_humDataInfo.guildName == player->GetGuildName();
}

bool PlayObject::IsGuildAlly(const PlayObject* player) const {
    if (!player || m_humDataInfo.guildName.empty() || player->GetGuildName().empty()) {
        return false;
    }

    // 如果是同一个行会，不算联盟
    if (IsGuildMember(player)) {
        return false;
    }

    auto& guildManager = GuildManager::GetInstance();
    auto* myGuild = guildManager.FindGuild(m_humDataInfo.guildName);
    auto* playerGuild = guildManager.FindGuild(player->GetGuildName());

    if (myGuild && playerGuild) {
        return myGuild->IsAlly(playerGuild);
    }

    return false;
}

bool PlayObject::IsGuildEnemy(const PlayObject* player) const {
    if (!player || m_humDataInfo.guildName.empty() || player->GetGuildName().empty()) {
        return false;
    }

    // 如果是同一个行会或联盟，不是敌人
    if (IsGuildMember(player) || IsGuildAlly(player)) {
        return false;
    }

    auto& guildManager = GuildManager::GetInstance();
    auto* myGuild = guildManager.FindGuild(m_humDataInfo.guildName);
    auto* playerGuild = guildManager.FindGuild(player->GetGuildName());

    if (myGuild && playerGuild) {
        return myGuild->IsWarWith(playerGuild);
    }

    return false;
}

void PlayObject::SetGuildInfo(const std::string& guildName, BYTE rank) {
    m_humDataInfo.guildName = guildName;
    m_humDataInfo.guildRank = rank;

    // 通知客户端行会信息变化
    SendDefMessage(Protocol::SM_CHANGEGUILDNAME, 0, 0, 0, 0);

    Logger::Debug("Player " + GetCharName() + " guild info updated: " + guildName + " rank " + std::to_string(rank));
}

void PlayObject::ClearGuildInfo() {
    m_humDataInfo.guildName.clear();
    m_humDataInfo.guildRank = 0;

    // 通知客户端行会信息变化
    SendDefMessage(Protocol::SM_CHANGEGUILDNAME, 0, 0, 0, 0);

    Logger::Debug("Player " + GetCharName() + " guild info cleared");
}

void PlayObject::RefreshShowName() {
    // 刷新显示名称，通知周围玩家名称变化
    // 对应原Delphi项目的RefShowName方法
    SendRefMsg(Protocol::SM_USERNAME, 0, 0, 0, 0, GetCharName());
}

// procedure TBaseObject.RefShowName();
// begin
//   SendRefMsg(RM_USERNAME, 0, 0, 0, 0, GetShowName);
// end;
void PlayObject::LogoutGame() {
    // 通知组队管理器玩家下线
    auto& groupManager = GroupManager::GetInstance();
    if (groupManager.IsInGroup(this)) {
        groupManager.OnPlayerLogout(this);
    }

    // 通知行会管理器玩家下线
    auto& guildManager = GuildManager::GetInstance();
    auto* guild = guildManager.GetPlayerGuild(GetCharName());
    if (guild) {
        guild->OnPlayerLogout(this);
    }

    // 保存数据
    SaveData();
}

bool PlayObject::OpenStorage(const std::string& password) {
    // 获取仓库管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* storageManager = gameEngine.GetStorageManager();

    if (!storageManager) {
        SendMessage("仓库系统暂时不可用", 0);
        return false;
    }

    // 尝试打开仓库
    if (storageManager->OpenStorage(this, password)) {
        // 发送仓库物品列表给客户端
        const auto& items = storageManager->GetStorageItems(this);
        DWORD gold = storageManager->GetStorageGold(this);

        // 发送仓库开启成功消息
        SendDefMessage(Protocol::SM_STORAGE_OK, 0, 0, 0, 0);

        // 发送仓库物品列表
        SendStorageItems(items, gold);

        SendMessage("仓库已打开", 0);
        return true;
    } else {
        SendDefMessage(Protocol::SM_STORAGEPASSWORD_FAIL, 0, 0, 0, 0);
        SendMessage("仓库密码错误", 0);
        return false;
    }
}

bool PlayObject::CloseStorage() {
    // 获取仓库管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* storageManager = gameEngine.GetStorageManager();

    if (!storageManager) {
        return false;
    }

    // 关闭仓库
    if (storageManager->CloseStorage(this)) {
        SendMessage("仓库已关闭", 0);
        return true;
    }

    return false;
}

bool PlayObject::StorageAddItem(const UserItem& item) {
    // 获取仓库管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* storageManager = gameEngine.GetStorageManager();

    if (!storageManager) {
        SendDefMessage(Protocol::SM_STORAGE_FAIL, 0, 0, 0, 0);
        return false;
    }

    // 检查仓库是否打开
    if (!storageManager->IsStorageOpen(this)) {
        SendDefMessage(Protocol::SM_STORAGE_FAIL, 0, 0, 0, 0);
        SendMessage("请先打开仓库", 0);
        return false;
    }

    // 检查仓库是否已满
    if (storageManager->IsStorageFull(this)) {
        SendDefMessage(Protocol::SM_STORAGE_FULL, 0, 0, 0, 0);
        SendMessage("仓库已满", 0);
        return false;
    }

    // 从背包中删除物品
    if (!DeleteBagItem(item.makeIndex)) {
        SendDefMessage(Protocol::SM_STORAGE_FAIL, 0, 0, 0, 0);
        SendMessage("物品不存在", 0);
        return false;
    }

    // 添加到仓库
    if (storageManager->StoreItem(this, item)) {
        SendDefMessage(Protocol::SM_STORAGE_OK, 0, 0, 0, 0);
        SendMessage("物品已存入仓库", 0);
        return true;
    } else {
        // 回滚：重新添加到背包
        AddBagItem(item);
        SendDefMessage(Protocol::SM_STORAGE_FAIL, 0, 0, 0, 0);
        SendMessage("存储失败", 0);
        return false;
    }
}

bool PlayObject::StorageTakeItem(WORD makeIndex, UserItem& outItem) {
    // 获取仓库管理器
    auto& gameEngine = GameEngine::GetInstance();
    auto* storageManager = gameEngine.GetStorageManager();

    if (!storageManager) {
        SendDefMessage(Protocol::SM_TAKEBACKSTORAGEITEM_FAIL, 0, 0, 0, 0);
        return false;
    }

    // 检查仓库是否打开
    if (!storageManager->IsStorageOpen(this)) {
        SendDefMessage(Protocol::SM_TAKEBACKSTORAGEITEM_FAIL, 0, 0, 0, 0);
        SendMessage("请先打开仓库", 0);
        return false;
    }

    // 检查背包是否已满
    if (IsBagFull()) {
        SendDefMessage(Protocol::SM_TAKEBACKSTORAGEITEM_FULLBAG, 0, 0, 0, 0);
        SendMessage("背包已满", 0);
        return false;
    }

    // 从仓库取出物品
    if (storageManager->TakeItem(this, makeIndex, outItem)) {
        // 添加到背包
        if (AddBagItem(outItem)) {
            SendDefMessage(Protocol::SM_TAKEBACKSTORAGEITEM_OK, 0, 0, 0, 0);
            SendMessage("物品已取出", 0);
            return true;
        } else {
            // 回滚：重新添加到仓库
            storageManager->StoreItem(this, outItem);
            SendDefMessage(Protocol::SM_TAKEBACKSTORAGEITEM_FAIL, 0, 0, 0, 0);
            SendMessage("背包空间不足", 0);
            return false;
        }
    } else {
        SendDefMessage(Protocol::SM_TAKEBACKSTORAGEITEM_FAIL, 0, 0, 0, 0);
        SendMessage("物品不存在", 0);
        return false;
    }
}

void PlayObject::SetLevel(WORD level) {
    m_humDataInfo.level = level;
    // TODO: 更新属性，发送等级变化通知
}

bool PlayObject::SpaceMove(const std::string& mapName, int x, int y) {
    if (!CanSpaceMove()) {
        return false;
    }

    // 保存当前位置
    std::string oldMapName = GetMapName();
    Point oldPos = GetCurrentPos();

    try {
        // 设置新位置
        m_humDataInfo.mapName = mapName;
        SetCurrentPos(Point{static_cast<WORD>(x), static_cast<WORD>(y)});

        // 发送地图切换消息
        SendDefMessage(Protocol::SM_SPACEMOVE_SHOW, static_cast<WORD>(x), static_cast<WORD>(y), 0, 0);
        SendDefMessage(Protocol::SM_MAPCHANGED, 0, 0, 0, 0);

        // 更新视野
        UpdateViewMap();

        Logger::Info("Player " + GetCharName() + " space moved from " + oldMapName +
                    " to " + mapName + " (" + std::to_string(x) + "," + std::to_string(y) + ")");

        return true;
    } catch (const std::exception& e) {
        // 恢复原位置
        m_humDataInfo.mapName = oldMapName;
        SetCurrentPos(oldPos);

        Logger::Error("SpaceMove failed for player " + GetCharName() + ": " + e.what());
        return false;
    }
}

bool PlayObject::CanSpaceMove() const {
    // 检查是否可以传送
    if (IsDead()) {
        return false;
    }

    // 检查是否在战斗状态
    // TODO: 添加战斗状态检查

    return true;
}

void PlayObject::SetGold(DWORD gold) {
    m_humDataInfo.gold = gold;
    SendGoldChanged();
}

void PlayObject::SendPacket(const std::vector<uint8_t>& packet) {
    // TODO: 通过网络管理器发送数据包到客户端
    // 这里需要与NetworkManager集成
    UpdateActiveTime();
}

void PlayObject::SendPacketToClient(const std::string& packet) {
    // 将字符串转换为字节数组并发送
    std::vector<uint8_t> data(packet.begin(), packet.end());
    SendPacket(data);

    // 记录发送的数据包（调试用）
    Logger::Debug("SendPacketToClient: " + std::to_string(packet.length()) + " bytes to " + GetCharName());
}

void PlayObject::SendMsg(BaseObject* obj, WORD msgId, int param1, int param2, int param3, int param4, const std::string& msg) {
    // 接收来自其他对象的消息（如其他玩家的动作、聊天等）
    if (!obj) return;

    // 根据消息类型处理不同的消息
    switch (msgId) {
        case Protocol::RM_HEAR: {
            // 听到其他玩家说话
            std::string fullMsg = obj->GetCharName() + ": " + msg;
            SendMessage(fullMsg, static_cast<BYTE>(param4));
            break;
        }
        case Protocol::RM_STRUCK: {
            // 看到其他对象受到攻击
            SendDefMessage(Protocol::SM_STRUCK, obj->GetObjectId(), param1, param2, param3);
            break;
        }
        case Protocol::RM_DEATH: {
            // 看到其他对象死亡
            SendDefMessage(Protocol::SM_NOWDEATH, obj->GetObjectId(), param1, param2, 0);
            break;
        }
        case Protocol::RM_WALK: {
            // 看到其他对象移动
            SendDefMessage(Protocol::SM_WALK, obj->GetObjectId(), param1, param2, param3);
            break;
        }
        case Protocol::RM_RUN: {
            // 看到其他对象跑步
            SendDefMessage(Protocol::SM_RUN, obj->GetObjectId(), param1, param2, param3);
            break;
        }
        case Protocol::RM_HIT: {
            // 看到其他对象攻击
            SendDefMessage(Protocol::SM_HIT, obj->GetObjectId(), param1, param2, param3);
            break;
        }
        case Protocol::RM_TURN: {
            // 看到其他对象转向
            SendDefMessage(Protocol::SM_TURN, obj->GetObjectId(), param1, 0, 0);
            break;
        }
        default:
            // 其他消息类型暂时不处理
            Logger::Debug("Unhandled SendMsg: msgId=" + std::to_string(msgId) + " from " + obj->GetCharName());
            break;
    }
}

// NPC交互系统实现
bool PlayObject::CanTalkToNPC(const BaseObject* npc) const {
    if (!npc || npc->GetObjectType() != ObjectType::NPC) {
        return false;
    }

    // 检查距离
    Point npcPos = npc->GetCurrentPos();
    int distance = std::max(std::abs(npcPos.x - m_currentPos.x),
                           std::abs(npcPos.y - m_currentPos.y));

    return distance <= 3; // NPC交互距离限制为3格
}

void PlayObject::TalkToNPC(BaseObject* npc) {
    if (!CanTalkToNPC(npc)) {
        SendMessage("距离太远，无法与NPC对话", 2);
        return;
    }

    // TODO: 实现NPC对话系统
    // 这需要与脚本引擎集成
    SendMessage("与 " + npc->GetCharName() + " 对话", 0);
}

// 状态检查和更新系统
void PlayObject::CheckStatusTimeOut() {
    DWORD currentTime = GetCurrentTime();

    // 检查防御提升状态
    if (m_boDefenceUp && currentTime >= m_dwDefenceUpTick) {
        m_boDefenceUp = false;
        RecalcAbilitys();
        SendDefMessage(Protocol::SM_DEFENCEUP, 0, 0, 0, 0);
    }

    // 检查魔防提升状态
    if (m_boMagDefenceUp && currentTime >= m_dwMagDefenceUpTick) {
        m_boMagDefenceUp = false;
        RecalcAbilitys();
        SendDefMessage(Protocol::SM_MAGDEFENCEUP, 0, 0, 0, 0);
    }

    // 检查魔法盾状态
    if (m_boMagBubbleDefence && currentTime >= m_dwMagBubbleDefenceTick) {
        m_boMagBubbleDefence = false;
        m_btMagBubbleDefenceLevel = 0;
        RecalcAbilitys();
        SendDefMessage(Protocol::SM_MAGBUBBLEDEFENCEUP, 0, 0, 0, 0);
    }

    // 检查中毒状态
    CheckPoisonStatus();

    // 检查其他状态...
}

void PlayObject::CheckPoisonStatus() {
    DWORD currentTime = GetCurrentTime();

    // 处理中毒伤害
    if (currentTime >= m_dwPoisoningTick + 1000) { // 每秒处理一次中毒
        m_dwPoisoningTick = currentTime;

        for (auto it = m_poisonList.begin(); it != m_poisonList.end();) {
            if (currentTime >= it->endTick) {
                // 中毒时间结束
                it = m_poisonList.erase(it);
            } else {
                // 造成中毒伤害
                int damage = it->point;
                if (damage > 0) {
                    DamageHealth(damage);

                    // 发送中毒伤害消息
                    SendDefMessage(Protocol::SM_STRUCK, damage, m_hp, 0, 0);
                }
                ++it;
            }
        }
    }
}

// 物品使用系统
bool PlayObject::UseItem(WORD makeIndex) {
    // 查找物品
    auto it = std::find_if(m_humDataInfo.bagItems.begin(), m_humDataInfo.bagItems.end(),
                          [makeIndex](const UserItem& item) {
                              return item.makeIndex == makeIndex;
                          });

    if (it == m_humDataInfo.bagItems.end()) {
        SendMessage("物品不存在", 2);
        return false;
    }

    UserItem& item = *it;

    // TODO: 根据物品类型执行不同的使用逻辑
    // 这里需要物品数据库支持

    // 示例：药水使用
    if (item.itemIndex >= 1000 && item.itemIndex <= 1010) {
        // 红药水
        int healAmount = 50 + (item.itemIndex - 1000) * 20;
        Heal(healAmount);

        // 消耗物品
        if (item.dura > 1) {
            item.dura--;
        } else {
            m_humDataInfo.bagItems.erase(it);
        }

        SendMessage("使用了 " + item.itemName, 0);
        SendDefMessage(Protocol::SM_EAT_OK, makeIndex, healAmount, 0, 0);
        return true;
    }

    SendMessage("无法使用该物品", 2);
    SendDefMessage(Protocol::SM_EAT_FAIL, makeIndex, 0, 0, 0);
    return false;
}

// 经验值和升级系统增强
void PlayObject::GainExpFromKill(const BaseObject* target) {
    if (!target || target == this) {
        return;
    }

    // 计算经验值
    DWORD expGain = CalculateExpGain(target);

    if (expGain > 0) {
        // 组队经验分享
        if (IsInGroup()) {
            ShareExpWithGroup(expGain);
        } else {
            GainExp(expGain);
        }

        // 发送击杀消息
        SendMessage("击败了 " + target->GetCharName() + "，获得 " + std::to_string(expGain) + " 点经验", 0);
    }
}

DWORD PlayObject::CalculateExpGain(const BaseObject* target) const {
    if (!target) return 0;

    // 基础经验值（根据目标等级）
    DWORD baseExp = target->GetLevel() * 10;

    // 等级差异调整
    int levelDiff = static_cast<int>(target->GetLevel()) - static_cast<int>(m_level);
    if (levelDiff > 10) {
        baseExp = baseExp * 2; // 高等级怪物额外奖励
    } else if (levelDiff < -10) {
        baseExp = baseExp / 2; // 低等级怪物经验减半
    }

    // 最小经验值保证
    return std::max(1U, baseExp);
}

void PlayObject::ShareExpWithGroup(DWORD totalExp) {
    // TODO: 实现组队经验分享
    // 这需要与组队系统集成

    // 暂时直接给自己经验
    GainExp(totalExp);
}

} // namespace MirServer