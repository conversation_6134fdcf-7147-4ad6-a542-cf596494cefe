// test_backup_guild_file.cpp - 测试BackupGuildFile功能
// 注意：BackupGuildFile在原项目中用于删除行会时的清理操作，不是解散通知
#include <gtest/gtest.h>
#include <memory>
#include <filesystem>
#include "../src/BaseObject/PlayObject.h"
#include "../src/GameEngine/GuildManager.h"
#include "../src/Common/Logger.h"

using namespace MirServer;

class BackupGuildFileTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化日志系统
        Logger::Initialize("test_backup_guild_file.log");

        // 初始化行会管理器
        GuildManager::GetInstance().Initialize();

        // 创建测试玩家
        player1 = std::make_shared<PlayObject>();
        player2 = std::make_shared<PlayObject>();
        player3 = std::make_shared<PlayObject>();

        // 设置玩家基本信息
        player1->SetCharName("GuildMaster");
        player1->SetLevel(50);

        player2->SetCharName("ViceLeader");
        player2->SetLevel(45);

        player3->SetCharName("Member1");
        player3->SetLevel(40);

        // 创建测试行会
        auto& guildManager = GuildManager::GetInstance();
        ASSERT_TRUE(guildManager.CreateGuild("TestGuildBackup", player1.get()));

        guild = guildManager.FindGuild("TestGuildBackup");
        ASSERT_NE(guild, nullptr);

        // 添加成员
        ASSERT_TRUE(guild->AddMember(player2.get(), GuildRank::VICE_CHIEF));
        ASSERT_TRUE(guild->AddMember(player3.get(), GuildRank::MEMBER));

        // 设置玩家行会信息
        player1->SetGuildInfo("TestGuildBackup", static_cast<BYTE>(GuildRank::CHIEF));
        player2->SetGuildInfo("TestGuildBackup", static_cast<BYTE>(GuildRank::VICE_CHIEF));
        player3->SetGuildInfo("TestGuildBackup", static_cast<BYTE>(GuildRank::MEMBER));

        // 添加一些测试数据
        guild->AddNotice("测试公告1");
        guild->AddNotice("测试公告2");
        guild->SetBuildPoint(1000);
        guild->SetAurae(500);
        guild->SetStability(800);
        guild->SetFlourishing(600);
        guild->SetGuildLevel(5);
        guild->SetGuildExp(25000);
        guild->SetGuildGold(100000);

        // 保存初始状态
        guild->SaveToFile();
    }

    void TearDown() override {
        // 清理测试文件
        std::filesystem::remove_all("GuildBase");

        // 清理
        GuildManager::GetInstance().Finalize();
        Logger::Finalize();
    }

    std::shared_ptr<PlayObject> player1;
    std::shared_ptr<PlayObject> player2;
    std::shared_ptr<PlayObject> player3;
    Guild* guild;
};

// 测试BackupGuildFile基本功能
TEST_F(BackupGuildFileTest, TestBasicBackupFunctionality) {
    // 验证初始状态
    EXPECT_EQ(guild->GetMemberCount(), 3);
    EXPECT_EQ(guild->GetNotices().size(), 2);
    EXPECT_EQ(guild->GetBuildPoint(), 1000);
    EXPECT_EQ(guild->GetGuildLevel(), 5);
    EXPECT_EQ(guild->GetGuildExp(), 25000);

    // 验证玩家行会信息
    EXPECT_EQ(player1->GetGuildName(), "TestGuildBackup");
    EXPECT_EQ(player2->GetGuildName(), "TestGuildBackup");
    EXPECT_EQ(player3->GetGuildName(), "TestGuildBackup");

    // 执行备份
    guild->BackupGuildFile();

    // 验证备份文件是否创建（原项目只备份数据文件，不备份配置）
    bool backupFileFound = false;

    for (const auto& entry : std::filesystem::directory_iterator("GuildBase")) {
        std::string filename = entry.path().filename().string();
        if (filename.find("TestGuildBackup.") != std::string::npos &&
            filename.find(".bak") != std::string::npos &&
            filename.find(".ini.bak") == std::string::npos) {
            backupFileFound = true;
            break;
        }
    }

    EXPECT_TRUE(backupFileFound) << "Backup file should be created";
}

// 测试数据清理功能（原项目逻辑：备份+清理）
TEST_F(BackupGuildFileTest, TestDataClearing) {
    // 执行备份（原项目中此操作会清理所有数据）
    guild->BackupGuildFile();

    // 验证行会数据被清理（与原Delphi项目一致）
    EXPECT_EQ(guild->GetMemberCount(), 0);
    EXPECT_EQ(guild->GetNotices().size(), 0);
    // 注意：原项目BackupGuildFile不重置这些属性，只清理成员和公告
    // EXPECT_EQ(guild->GetBuildPoint(), 0);
    // EXPECT_EQ(guild->GetAurae(), 0);

    // 验证玩家行会信息被清理（原项目逻辑：m_MyGuild = nil, RefRankInfo(0, '')）
    EXPECT_TRUE(player1->GetGuildName().empty());
    EXPECT_TRUE(player2->GetGuildName().empty());
    EXPECT_TRUE(player3->GetGuildName().empty());
    EXPECT_EQ(player1->GetGuildRank(), 0);
    EXPECT_EQ(player2->GetGuildRank(), 0);
    EXPECT_EQ(player3->GetGuildRank(), 0);
}

// 测试备份文件内容
TEST_F(BackupGuildFileTest, TestBackupFileContent) {
    // 执行备份
    guild->BackupGuildFile();

    // 查找备份文件
    std::string backupFile;
    for (const auto& entry : std::filesystem::directory_iterator("GuildBase")) {
        std::string filename = entry.path().filename().string();
        if (filename.find("TestGuildBackup.") != std::string::npos &&
            filename.find(".bak") != std::string::npos &&
            filename.find(".ini.bak") == std::string::npos) {
            backupFile = entry.path().string();
            break;
        }
    }

    ASSERT_FALSE(backupFile.empty()) << "Backup file should exist";

    // 读取备份文件内容
    std::ifstream file(backupFile);
    ASSERT_TRUE(file.is_open()) << "Should be able to open backup file";

    std::string content;
    std::string line;
    while (std::getline(file, line)) {
        content += line + "\n";
    }
    file.close();

    // 验证备份文件包含原始数据
    EXPECT_TRUE(content.find("测试公告1") != std::string::npos) << "Backup should contain notice 1";
    EXPECT_TRUE(content.find("测试公告2") != std::string::npos) << "Backup should contain notice 2";
    EXPECT_TRUE(content.find("GuildMaster") != std::string::npos) << "Backup should contain guild master";
    EXPECT_TRUE(content.find("ViceLeader") != std::string::npos) << "Backup should contain vice leader";
    EXPECT_TRUE(content.find("Member1") != std::string::npos) << "Backup should contain member";
}

// 测试原项目兼容性（BackupGuildFile不备份配置文件）
TEST_F(BackupGuildFileTest, TestOriginalProjectCompatibility) {
    // 设置一些配置
    guild->SetConfigBool("General.AutoSave", true);
    guild->SetConfigInt("General.MaxMembers", 150);
    guild->SetConfigString("General.Description", "测试行会");

    // 执行备份
    guild->BackupGuildFile();

    // 验证配置文件没有被备份（与原项目一致）
    bool configBackupFound = false;
    for (const auto& entry : std::filesystem::directory_iterator("GuildBase")) {
        std::string filename = entry.path().filename().string();
        if (filename.find("TestGuildBackup.") != std::string::npos &&
            filename.find(".ini.bak") != std::string::npos) {
            configBackupFound = true;
            break;
        }
    }

    EXPECT_FALSE(configBackupFound) << "Config backup should NOT be created (original project behavior)";

    // 验证配置仍然存在（原项目不清理配置）
    EXPECT_TRUE(guild->GetConfigBool("General.AutoSave", false));
    EXPECT_EQ(guild->GetConfigInt("General.MaxMembers", 0), 150);
    EXPECT_EQ(guild->GetConfigString("General.Description", ""), "测试行会");
}

// 测试重新加载功能
TEST_F(BackupGuildFileTest, TestReloadAfterBackup) {
    // 执行备份
    guild->BackupGuildFile();

    // 验证数据被清理
    EXPECT_EQ(guild->GetMemberCount(), 0);
    EXPECT_EQ(guild->GetNotices().size(), 0);

    // 重新加载（应该加载清理后的状态）
    EXPECT_TRUE(guild->LoadFromFile());

    // 验证加载的是清理后的状态
    EXPECT_EQ(guild->GetMemberCount(), 0);
    EXPECT_EQ(guild->GetNotices().size(), 0);
    EXPECT_EQ(guild->GetGuildLevel(), 1);
    EXPECT_EQ(guild->GetGuildExp(), 0);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
