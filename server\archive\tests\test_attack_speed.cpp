// test_attack_speed.cpp - 攻击速度系统测试
#include <gtest/gtest.h>
#include "../src/BaseObject/PlayObject.h"
#include "../src/Common/DataTypes.h"

using namespace MirServer;

class AttackSpeedTest : public ::testing::Test {
protected:
    void SetUp() override {
        player = std::make_unique<PlayObject>();
        player->Initialize();
    }

    void TearDown() override {
        player.reset();
    }

    std::unique_ptr<PlayObject> player;
};

// 测试默认攻击速度
TEST_F(AttackSpeedTest, DefaultAttackSpeed) {
    // 默认攻击速度值应该是0（根据原Delphi项目）
    DWORD attackSpeed = player->GetAttackSpeed();

    // 根据公式：attackInterval = max(0, 1000 - 0 * 10) = 1000
    // 所以默认攻击间隔应该是1000ms
    EXPECT_EQ(attackSpeed, 1000);
}

// 测试攻击速度计算公式
TEST_F(AttackSpeedTest, AttackSpeedFormula) {
    // 测试原Delphi项目的攻击速度计算公式
    // dwAttackTime := _MAX(0, Integer(g_Config.dwHitIntervalTime) - m_nHitSpeed * g_Config.ClientConf.btItemSpeed)

    // 基础间隔时间：1000ms
    // 默认攻击速度：0（修正后的值）
    // 物品速度系数：10
    // 计算结果：max(0, 1000 - 0 * 10) = 1000ms

    DWORD expectedSpeed = std::max(200U, std::min(2000U, 1000U - 0 * 10));
    DWORD actualSpeed = player->GetAttackSpeed();

    EXPECT_EQ(actualSpeed, expectedSpeed);
}

// 测试武器对攻击速度的影响
TEST_F(AttackSpeedTest, WeaponAttackSpeed) {
    // 创建一个武器
    UserItem weapon;
    weapon.itemIndex = 1;
    weapon.makeIndex = 1001;
    weapon.dura = 5000; // 耐久度5000，按简化计算会增加5点攻击速度

    // 装备武器
    player->TakeOnItem(weapon, EquipPosition::WEAPON);

    // 重新计算攻击速度
    player->RecalcHitSpeed();

    // 新的攻击速度值应该是 0 + 5 = 5
    // 攻击间隔：max(0, 1000 - 5 * 10) = 950ms
    DWORD expectedSpeed = std::max(200U, std::min(2000U, 1000U - 5 * 10));
    DWORD actualSpeed = player->GetAttackSpeed();

    EXPECT_EQ(actualSpeed, expectedSpeed);
}

// 测试攻击速度的边界值
TEST_F(AttackSpeedTest, AttackSpeedBoundaries) {
    // 测试最小攻击间隔（200ms）
    // 当攻击速度值很高时，应该限制在最小值

    // 模拟一个非常高的攻击速度值
    // 如果攻击速度值为100，计算结果为：max(0, 1000 - 100 * 10) = 0
    // 但应该被限制为最小值200ms

    DWORD attackSpeed = player->GetAttackSpeed();
    EXPECT_GE(attackSpeed, 200U); // 不应该小于200ms
    EXPECT_LE(attackSpeed, 2000U); // 不应该大于2000ms
}

// 测试RecalcHitSpeed方法
TEST_F(AttackSpeedTest, RecalcHitSpeed) {
    // 获取初始攻击速度
    DWORD initialSpeed = player->GetAttackSpeed();

    // 调用RecalcHitSpeed
    player->RecalcHitSpeed();

    // 攻击速度应该保持一致（因为没有装备变化）
    DWORD newSpeed = player->GetAttackSpeed();
    EXPECT_EQ(initialSpeed, newSpeed);
}

// 测试装备变化时攻击速度的重新计算
TEST_F(AttackSpeedTest, EquipmentChangeRecalc) {
    // 获取初始攻击速度
    DWORD initialSpeed = player->GetAttackSpeed();

    // 装备一个武器
    UserItem weapon;
    weapon.itemIndex = 1;
    weapon.makeIndex = 1001;
    weapon.dura = 3000; // 会增加3点攻击速度

    player->TakeOnItem(weapon, EquipPosition::WEAPON);

    // 攻击速度应该发生变化
    DWORD newSpeed = player->GetAttackSpeed();
    EXPECT_NE(initialSpeed, newSpeed);

    // 卸下武器
    UserItem removedWeapon;
    player->TakeOffItem(EquipPosition::WEAPON, removedWeapon);

    // 攻击速度应该恢复到初始值
    DWORD restoredSpeed = player->GetAttackSpeed();
    EXPECT_EQ(initialSpeed, restoredSpeed);
}

// 测试攻击速度与原Delphi项目的兼容性
TEST_F(AttackSpeedTest, DelphiCompatibility) {
    // 验证攻击速度计算与原Delphi项目的兼容性

    // 原Delphi项目的默认值：
    // m_nHitSpeed := 0 (攻击速度默认值为0)
    // g_Config.dwHitIntervalTime = 1000 (假设)
    // g_Config.ClientConf.btItemSpeed = 10 (假设)

    // 计算公式：dwAttackTime := _MAX(0, Integer(g_Config.dwHitIntervalTime) - m_nHitSpeed * g_Config.ClientConf.btItemSpeed)
    // 结果：max(0, 1000 - 0 * 10) = 1000

    DWORD expectedAttackTime = 1000;
    DWORD actualAttackTime = player->GetAttackSpeed();

    EXPECT_EQ(actualAttackTime, expectedAttackTime);
}

// 测试多种武器类型的攻击速度
TEST_F(AttackSpeedTest, DifferentWeaponTypes) {
    // 测试不同耐久度的武器对攻击速度的影响

    struct WeaponTest {
        WORD dura;
        int expectedSpeedBonus;
    };

    std::vector<WeaponTest> weaponTests = {
        {1000, 1},   // 耐久度1000，增加1点攻击速度
        {2000, 2},   // 耐久度2000，增加2点攻击速度
        {5000, 5},   // 耐久度5000，增加5点攻击速度
        {10000, 10}, // 耐久度10000，增加10点攻击速度
    };

    for (const auto& test : weaponTests) {
        // 创建武器
        UserItem weapon;
        weapon.itemIndex = 1;
        weapon.makeIndex = 1001;
        weapon.dura = test.dura;

        // 装备武器
        player->TakeOnItem(weapon, EquipPosition::WEAPON);

        // 计算期望的攻击间隔
        int expectedHitSpeed = 0 + test.expectedSpeedBonus; // 基础攻击速度为0
        DWORD expectedInterval = std::max(200U, std::min(2000U, 1000U - expectedHitSpeed * 10));

        // 验证攻击速度
        DWORD actualInterval = player->GetAttackSpeed();
        EXPECT_EQ(actualInterval, expectedInterval)
            << "武器耐久度: " << test.dura
            << ", 期望攻击间隔: " << expectedInterval
            << ", 实际攻击间隔: " << actualInterval;

        // 卸下武器
        UserItem removedWeapon;
        player->TakeOffItem(EquipPosition::WEAPON, removedWeapon);
    }
}
