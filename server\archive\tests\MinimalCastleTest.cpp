// MinimalCastleTest.cpp - 最小化城堡系统测试
#include "CastleManager.h"
#include "GuildManager.h"
#include "../Common/Logger.h"
#include <iostream>

using namespace MirServer;

int main() {
    std::cout << "最小化城堡系统测试" << std::endl;
    std::cout << "==================" << std::endl;
    
    try {
        // 设置日志
        Logger::SetLogFile("minimal_castle_test.log");
        Logger::SetLogLevel(LogLevel::LOG_INFO);
        
        std::cout << "1. 初始化行会管理器..." << std::endl;
        GuildManager::GetInstance().Initialize();
        std::cout << "   完成" << std::endl;
        
        std::cout << "2. 初始化城堡管理器..." << std::endl;
        CastleManager::GetInstance().Initialize();
        std::cout << "   完成" << std::endl;
        
        std::cout << "3. 获取城堡信息..." << std::endl;
        CastleManager& manager = CastleManager::GetInstance();
        std::cout << "   城堡数量: " << manager.GetCastleCount() << std::endl;
        
        Castle* castle = manager.FindCastle("沙巴克");
        if (castle) {
            std::cout << "   找到城堡: " << castle->GetCastleName() << std::endl;
            std::cout << "   城堡地图: " << castle->GetMapName() << std::endl;
            std::cout << "   回城坐标: (" << castle->GetHomeX() << ", " << castle->GetHomeY() << ")" << std::endl;
            std::cout << "   总金币: " << castle->GetTotalGold() << std::endl;
            
            std::cout << "4. 测试基本功能..." << std::endl;
            
            // 测试收入（不使用GetCurrentTime）
            std::cout << "   添加收入1000金币..." << std::endl;
            castle->IncomeGold(1000);
            std::cout << "   当前总金币: " << castle->GetTotalGold() << std::endl;
            
            // 测试技术等级
            std::cout << "   设置技术等级为5..." << std::endl;
            castle->SetTechLevel(5);
            std::cout << "   当前技术等级: " << castle->GetTechLevel() << std::endl;
            
            // 测试力量值
            std::cout << "   设置力量值为300..." << std::endl;
            castle->SetPower(300);
            std::cout << "   当前力量值: " << castle->GetPower() << std::endl;
            
            // 测试攻击者管理（使用固定时间戳）
            std::cout << "   添加攻击者..." << std::endl;
            bool result = castle->AddAttacker("测试行会1", 12345);
            std::cout << "   添加攻击者结果: " << (result ? "成功" : "失败") << std::endl;
            
            result = castle->AddAttacker("测试行会2", 12346);
            std::cout << "   添加第二个攻击者结果: " << (result ? "成功" : "失败") << std::endl;
            
            std::cout << "   攻击者列表: " << castle->GetAttackerList() << std::endl;
            
            // 测试防御单元修复
            std::cout << "   测试防御单元修复..." << std::endl;
            result = castle->RepairDoor();
            std::cout << "   修复城门结果: " << (result ? "成功" : "失败") << std::endl;
            
            result = castle->RepairWall(0);
            std::cout << "   修复左墙结果: " << (result ? "成功" : "失败") << std::endl;
            
            // 测试保存
            std::cout << "   保存城堡数据..." << std::endl;
            castle->Save();
            std::cout << "   保存完成" << std::endl;
            
        } else {
            std::cout << "   未找到沙巴克城堡！" << std::endl;
        }
        
        std::cout << "5. 测试城堡管理器功能..." << std::endl;
        
        std::vector<std::string> nameList;
        manager.GetCastleNameList(nameList);
        std::cout << "   城堡名称列表:" << std::endl;
        for (const auto& name : nameList) {
            std::cout << "     - " << name << std::endl;
        }
        
        std::vector<std::string> goldInfo;
        manager.GetCastleGoldInfo(goldInfo);
        std::cout << "   城堡金币信息:" << std::endl;
        for (const auto& info : goldInfo) {
            std::cout << "     " << info << std::endl;
        }
        
        std::cout << "6. 清理..." << std::endl;
        CastleManager::GetInstance().Finalize();
        GuildManager::GetInstance().Finalize();
        std::cout << "   清理完成" << std::endl;
        
        std::cout << "\n=== 测试完成 ===" << std::endl;
        std::cout << "所有城堡系统功能测试成功！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
