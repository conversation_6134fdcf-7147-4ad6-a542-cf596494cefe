#include "src/GameEngine/SimplePKManager.h"
#include <iostream>

using namespace MirServer;

int main() {
    std::cout << "=== 最小化PK系统测试 ===" << std::endl;

    try {
        // 初始化PK管理器
        auto& pkManager = SimplePKManager::GetInstance();
        pkManager.Initialize();

        std::cout << "✓ PK管理器初始化成功" << std::endl;

        // 测试1: 配置管理
        std::cout << "\n--- 测试1: 配置管理 ---" << std::endl;
        pkManager.SetKillAddPKValue(100);
        pkManager.SetRedNamePKValue(200);
        pkManager.SetYellowNamePKValue(100);
        pkManager.SetPKFlagTime(300);
        pkManager.SetRedNameTime(3600);

        std::cout << "杀人增加PK值: " << pkManager.GetKillAddPKValue() << std::endl;
        std::cout << "红名PK值阈值: " << pkManager.GetRedNamePKValue() << std::endl;
        std::cout << "黄名PK值阈值: " << pkManager.GetYellowNamePKValue() << std::endl;
        std::cout << "PK标记时间: " << pkManager.GetPKFlagTime() << " 秒" << std::endl;
        std::cout << "红名持续时间: " << pkManager.GetRedNameTime() << " 秒" << std::endl;

        // 测试2: 行会战系统
        std::cout << "\n--- 测试2: 行会战系统 ---" << std::endl;

        // 开始行会战
        bool warStarted = pkManager.StartGuildWar("沙巴克", "法师联盟", 30);
        std::cout << "开始行会战 [沙巴克 vs 法师联盟]: " << (warStarted ? "✓ 成功" : "✗ 失败") << std::endl;

        // 检查行会战状态
        bool isWar = pkManager.IsGuildWar("沙巴克", "法师联盟");
        std::cout << "行会战状态检查: " << (isWar ? "✓ 进行中" : "✗ 未进行") << std::endl;

        // 获取活跃行会战列表
        auto activeWars = pkManager.GetActiveGuildWars();
        std::cout << "当前活跃行会战数量: " << activeWars.size() << std::endl;

        if (!activeWars.empty()) {
            const auto& war = activeWars[0];
            std::cout << "行会战详情: " << war.guild1 << " vs " << war.guild2
                     << " (持续 " << war.duration << " 分钟)" << std::endl;
        }

        // 测试3: 重复开始行会战（应该失败）
        std::cout << "\n--- 测试3: 重复行会战检测 ---" << std::endl;
        bool duplicateWar = pkManager.StartGuildWar("沙巴克", "法师联盟", 20);
        std::cout << "重复开始行会战: " << (duplicateWar ? "✗ 成功（错误）" : "✓ 失败（正确）") << std::endl;

        // 测试4: 开始另一个行会战
        std::cout << "\n--- 测试4: 多个行会战 ---" << std::endl;
        bool secondWar = pkManager.StartGuildWar("战士公会", "道士联盟", 45);
        std::cout << "开始第二个行会战 [战士公会 vs 道士联盟]: " << (secondWar ? "✓ 成功" : "✗ 失败") << std::endl;

        activeWars = pkManager.GetActiveGuildWars();
        std::cout << "当前活跃行会战数量: " << activeWars.size() << std::endl;

        // 测试5: 结束行会战
        std::cout << "\n--- 测试5: 结束行会战 ---" << std::endl;
        bool warEnded = pkManager.EndGuildWar("沙巴克", "法师联盟");
        std::cout << "结束行会战 [沙巴克 vs 法师联盟]: " << (warEnded ? "✓ 成功" : "✗ 失败") << std::endl;

        activeWars = pkManager.GetActiveGuildWars();
        std::cout << "结束后活跃行会战数量: " << activeWars.size() << std::endl;

        // 测试6: 结束不存在的行会战
        std::cout << "\n--- 测试6: 结束不存在的行会战 ---" << std::endl;
        bool endNonExistent = pkManager.EndGuildWar("不存在公会1", "不存在公会2");
        std::cout << "结束不存在的行会战: " << (endNonExistent ? "✗ 成功（错误）" : "✓ 失败（正确）") << std::endl;

        // 测试7: 检查不存在的行会战
        std::cout << "\n--- 测试7: 检查不存在的行会战 ---" << std::endl;
        bool nonExistentWar = pkManager.IsGuildWar("不存在公会1", "不存在公会2");
        std::cout << "检查不存在的行会战: " << (nonExistentWar ? "✗ 存在（错误）" : "✓ 不存在（正确）") << std::endl;

        // 测试8: 更新机制
        std::cout << "\n--- 测试8: 更新机制 ---" << std::endl;
        pkManager.Update();
        std::cout << "✓ PK管理器更新完成" << std::endl;

        // 测试9: 清理剩余的行会战
        std::cout << "\n--- 测试9: 清理 ---" << std::endl;
        pkManager.EndGuildWar("战士公会", "道士联盟");

        activeWars = pkManager.GetActiveGuildWars();
        std::cout << "清理后活跃行会战数量: " << activeWars.size() << std::endl;

        // 清理
        pkManager.Finalize();
        std::cout << "✓ PK管理器清理完成" << std::endl;

        std::cout << "\n=== 最小化PK系统测试完成 ===" << std::endl;
        std::cout << "🎉 所有测试通过!" << std::endl;

        return 0;

    } catch (const std::exception& e) {
        std::cerr << "❌ 测试失败: " << e.what() << std::endl;
        return 1;
    }
}
