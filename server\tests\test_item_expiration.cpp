// test_item_expiration.cpp - 物品过期检查测试
#include <gtest/gtest.h>
#include "../src/GameEngine/UserEngine.h"
#include "../src/GameEngine/ItemManager.h"
#include "../src/BaseObject/PlayObject.h"
#include "../src/Common/Logger.h"
#include <memory>
#include <cstring>

using namespace MirServer;

class ItemExpirationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化日志系统
        Logger::Initialize("test_item_expiration.log", LogLevel::DEBUG);

        // 创建ItemManager
        itemManager = std::make_unique<ItemManager>();
        itemManager->Initialize();

        // 创建UserEngine
        userEngine = std::make_unique<UserEngine>();
        userEngine->Initialize(nullptr, itemManager.get());

        // 创建测试玩家
        HumDataInfo humData;
        humData.charName = "TestPlayer";
        humData.job = JobType::WARRIOR;
        humData.level = 10;
        player = userEngine->CreatePlayer(humData);

        // 添加测试物品数据
        SetupTestItems();
    }

    void TearDown() override {
        userEngine.reset();
        itemManager.reset();
        Logger::Finalize();
    }

    void SetupTestItems() {
        // 创建测试用的StdItem
        normalItem = std::make_unique<StdItem>();
        normalItem->idx = 1001;
        normalItem->name = "普通剑";
        normalItem->source = 0;
        normalItem->stdMode = 5;

        gmItem = std::make_unique<StdItem>();
        gmItem->idx = 1002;
        gmItem->name = "GM物品";
        gmItem->source = 99; // GM物品
        gmItem->stdMode = 5;

        tempItem = std::make_unique<StdItem>();
        tempItem->idx = 1003;
        tempItem->name = "临时装备";
        tempItem->source = 0;
        tempItem->stdMode = 55; // 临时物品类型

        activityItem = std::make_unique<StdItem>();
        activityItem->idx = 1004;
        activityItem->name = "活动物品(限时)";
        activityItem->source = 0;
        activityItem->stdMode = 10;

        // 将测试物品添加到ItemManager
        // 注意：这里需要ItemManager支持添加测试物品的方法
    }

    UserItem CreateTestUserItem(int itemIndex, const std::string& name) {
        UserItem item;
        item.itemIndex = itemIndex;
        item.itemName = name;
        item.makeIndex = 1000 + itemIndex;
        item.dura = 100;
        item.duraMax = 100;
        memset(item.btValue, 0, sizeof(item.btValue));
        return item;
    }

    void SetItemExpireTime(UserItem& item, DWORD expireTime, int offset = 8) {
        *reinterpret_cast<DWORD*>(&item.btValue[offset]) = expireTime;
    }

    std::unique_ptr<ItemManager> itemManager;
    std::unique_ptr<UserEngine> userEngine;
    std::shared_ptr<PlayObject> player;

    std::unique_ptr<StdItem> normalItem;
    std::unique_ptr<StdItem> gmItem;
    std::unique_ptr<StdItem> tempItem;
    std::unique_ptr<StdItem> activityItem;
};

// 测试普通物品不会被识别为限时物品
TEST_F(ItemExpirationTest, TestNormalItemNotTimeLimited) {
    DWORD expireTime = 0;
    bool isTimeLimited = userEngine->IsTimeLimitedItem(normalItem.get(), nullptr, expireTime);

    EXPECT_FALSE(isTimeLimited);
    EXPECT_EQ(expireTime, 0);
}

// 测试GM物品（source=99）被识别为限时物品
TEST_F(ItemExpirationTest, TestGMItemIsTimeLimited) {
    UserItem testItem = CreateTestUserItem(1002, "GM物品");
    DWORD testExpireTime = GetCurrentTime() + 3600000; // 1小时后过期
    SetItemExpireTime(testItem, testExpireTime, 8);

    DWORD expireTime = 0;
    bool isTimeLimited = userEngine->IsTimeLimitedItem(gmItem.get(), &testItem, expireTime);

    EXPECT_TRUE(isTimeLimited);
    EXPECT_EQ(expireTime, testExpireTime);
}

// 测试临时装备（stdMode 50-59）被识别为限时物品
TEST_F(ItemExpirationTest, TestTempItemIsTimeLimited) {
    UserItem testItem = CreateTestUserItem(1003, "临时装备");
    DWORD testExpireTime = GetCurrentTime() + 1800000; // 30分钟后过期
    SetItemExpireTime(testItem, testExpireTime, 10);

    DWORD expireTime = 0;
    bool isTimeLimited = userEngine->IsTimeLimitedItem(tempItem.get(), &testItem, expireTime);

    EXPECT_TRUE(isTimeLimited);
    EXPECT_EQ(expireTime, testExpireTime);
}

// 测试通过btValue[13]标志识别限时物品
TEST_F(ItemExpirationTest, TestFlagBasedTimeLimitedItem) {
    UserItem testItem = CreateTestUserItem(1001, "普通剑");
    testItem.btValue[13] = 1; // 设置限时标志
    DWORD testExpireTime = GetCurrentTime() + 7200000; // 2小时后过期
    SetItemExpireTime(testItem, testExpireTime, 9);

    DWORD expireTime = 0;
    bool isTimeLimited = userEngine->IsTimeLimitedItem(normalItem.get(), &testItem, expireTime);

    EXPECT_TRUE(isTimeLimited);
    EXPECT_EQ(expireTime, testExpireTime);
}

// 测试通过物品名称识别限时物品
TEST_F(ItemExpirationTest, TestNameBasedTimeLimitedItem) {
    UserItem testItem = CreateTestUserItem(1004, "活动物品(限时)");
    DWORD testExpireTime = GetCurrentTime() + 900000; // 15分钟后过期
    SetItemExpireTime(testItem, testExpireTime, 6);

    DWORD expireTime = 0;
    bool isTimeLimited = userEngine->IsTimeLimitedItem(activityItem.get(), &testItem, expireTime);

    EXPECT_TRUE(isTimeLimited);
    EXPECT_EQ(expireTime, testExpireTime);
}

// 测试过期物品检查逻辑
TEST_F(ItemExpirationTest, TestExpiredItemRemoval) {
    // 创建一个已过期的物品
    UserItem expiredItem = CreateTestUserItem(1002, "过期GM物品");
    DWORD pastTime = GetCurrentTime() - 1000; // 1秒前就过期了
    SetItemExpireTime(expiredItem, pastTime, 8);

    // 添加到玩家背包
    auto& bagItems = const_cast<std::vector<UserItem>&>(player->GetBagItems());
    bagItems.push_back(expiredItem);

    size_t initialCount = bagItems.size();

    // 执行过期检查
    userEngine->CheckItemExpiration(player, GetCurrentTime());

    // 验证过期物品被移除
    EXPECT_LT(bagItems.size(), initialCount);
}

// 测试未过期物品不被移除
TEST_F(ItemExpirationTest, TestNonExpiredItemNotRemoved) {
    // 创建一个未过期的物品
    UserItem validItem = CreateTestUserItem(1002, "有效GM物品");
    DWORD futureTime = GetCurrentTime() + 3600000; // 1小时后过期
    SetItemExpireTime(validItem, futureTime, 8);

    // 添加到玩家背包
    auto& bagItems = const_cast<std::vector<UserItem>&>(player->GetBagItems());
    bagItems.push_back(validItem);

    size_t initialCount = bagItems.size();

    // 执行过期检查
    userEngine->CheckItemExpiration(player, GetCurrentTime());

    // 验证有效物品未被移除
    EXPECT_EQ(bagItems.size(), initialCount);
}

// 测试永不过期物品（expireTime=0）
TEST_F(ItemExpirationTest, TestNeverExpireItem) {
    UserItem neverExpireItem = CreateTestUserItem(1002, "永久GM物品");
    SetItemExpireTime(neverExpireItem, 0, 8); // expireTime=0表示永不过期

    // 添加到玩家背包
    auto& bagItems = const_cast<std::vector<UserItem>&>(player->GetBagItems());
    bagItems.push_back(neverExpireItem);

    size_t initialCount = bagItems.size();

    // 执行过期检查
    userEngine->CheckItemExpiration(player, GetCurrentTime());

    // 验证永不过期物品未被移除
    EXPECT_EQ(bagItems.size(), initialCount);
}

// 测试过期装备直接消失处理（遵循原项目逻辑）
TEST_F(ItemExpirationTest, TestExpiredEquipmentDisappears) {
    // 注意：根据原项目逻辑，过期的装备是直接消失的，而不是自动卸下到背包
    // 这个测试验证过期装备的正确处理方式

    // 创建一个已过期的装备
    UserItem expiredEquip = CreateTestUserItem(1002, "过期GM装备");
    DWORD pastTime = GetCurrentTime() - 1000; // 1秒前就过期了
    SetItemExpireTime(expiredEquip, pastTime, 8);

    // 模拟装备到玩家身上的过程
    // 注意：这里需要PlayObject支持ClearEquipSlot方法
    // 在实际测试中，我们可以通过检查装备槽是否被清空来验证

    // 执行过期检查
    userEngine->CheckItemExpiration(player, GetCurrentTime());

    // 验证：过期装备应该直接消失，不会进入背包
    // 这遵循原项目的逻辑，与背包物品的处理方式不同
    EXPECT_TRUE(true); // 占位符，实际测试需要检查装备槽状态
}

// 性能测试：大量物品过期检查
TEST_F(ItemExpirationTest, TestPerformanceWithManyItems) {
    auto& bagItems = const_cast<std::vector<UserItem>&>(player->GetBagItems());

    // 添加大量测试物品
    for (int i = 0; i < 1000; ++i) {
        UserItem item = CreateTestUserItem(1001, "普通物品" + std::to_string(i));
        bagItems.push_back(item);
    }

    auto startTime = std::chrono::high_resolution_clock::now();

    // 执行过期检查
    userEngine->CheckItemExpiration(player, GetCurrentTime());

    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

    // 性能应该在合理范围内（比如小于10ms）
    EXPECT_LT(duration.count(), 10) << "Item expiration check should be fast";
}
