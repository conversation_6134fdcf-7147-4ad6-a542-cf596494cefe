// 核心功能集成测试
#include "GameEngine.h"
#include "StorageManager.h"
#include "TradeManager.h"
#include "QuestManager.h"
#include "MiniMapManager.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>

using namespace MirServer;

// 创建测试玩家
std::unique_ptr<PlayObject> CreateTestPlayer(const std::string& name) {
    auto player = std::make_unique<PlayObject>();
    // 设置基本信息
    player->SetCharName(name);
    player->SetLevel(10);
    player->SetJob(JobType::WARRIOR);
    player->IncGold(1000); // 给予1000金币
    return player;
}

// 测试仓库系统
void TestStorageSystem() {
    Logger::Info("=== Testing Storage System ===");

    auto& engine = GameEngine::GetInstance();
    auto* storageManager = engine.GetStorageManager();

    if (!storageManager) {
        Logger::Error("StorageManager not available");
        return;
    }

    // 创建测试玩家
    auto player = CreateTestPlayer("TestPlayer1");

    // 测试打开仓库
    bool opened = storageManager->OpenStorage(player.get(), "123456");
    Logger::Info("Storage opened: " + std::string(opened ? "SUCCESS" : "FAILED"));

    // 测试存储金币
    bool goldStored = storageManager->StoreGold(player.get(), 500);
    Logger::Info("Gold stored: " + std::string(goldStored ? "SUCCESS" : "FAILED"));

    // 测试查询仓库金币
    MirServer::DWORD storageGold = storageManager->GetStorageGold(player.get());
    Logger::Info("Storage gold: " + std::to_string(storageGold));

    // 测试关闭仓库
    bool closed = storageManager->CloseStorage(player.get());
    Logger::Info("Storage closed: " + std::string(closed ? "SUCCESS" : "FAILED"));

    // 获取统计信息
    auto stats = storageManager->GetStatistics();
    Logger::Info("Storage Statistics - Total: " + std::to_string(stats.totalStorages) +
                 ", Active: " + std::to_string(stats.activeStorages));
}

// 测试交易系统
void TestTradeSystem() {
    Logger::Info("=== Testing Trade System ===");

    auto& engine = GameEngine::GetInstance();
    auto* tradeManager = engine.GetTradeManager();

    if (!tradeManager) {
        Logger::Error("TradeManager not available");
        return;
    }

    // 创建两个测试玩家
    auto player1 = CreateTestPlayer("Trader1");
    auto player2 = CreateTestPlayer("Trader2");

    // 设置玩家位置（确保距离足够近）
    Point pos1 = {100, 100};
    Point pos2 = {101, 101};
    player1->SetCurrentPos(pos1);
    player2->SetCurrentPos(pos2);

    // 测试交易请求
    bool requested = tradeManager->RequestTrade(player1.get(), player2.get());
    Logger::Info("Trade requested: " + std::string(requested ? "SUCCESS" : "FAILED"));

    if (requested) {
        // 测试接受交易
        bool accepted = tradeManager->AcceptTrade(player2.get(), player1->GetCharName());
        Logger::Info("Trade accepted: " + std::string(accepted ? "SUCCESS" : "FAILED"));

        if (accepted) {
            // 测试设置交易金币
            bool goldSet = tradeManager->SetTradeGold(player1.get(), 100);
            Logger::Info("Trade gold set: " + std::string(goldSet ? "SUCCESS" : "FAILED"));

            // 测试锁定交易
            bool locked1 = tradeManager->LockTrade(player1.get());
            bool locked2 = tradeManager->LockTrade(player2.get());
            Logger::Info("Trade locked: " + std::string(locked1 && locked2 ? "SUCCESS" : "FAILED"));
        }
    }

    // 获取统计信息
    auto stats = tradeManager->GetStatistics();
    Logger::Info("Trade Statistics - Active: " + std::to_string(stats.activeTrades) +
                 ", Completed: " + std::to_string(stats.completedTrades));
}

// 测试任务系统
void TestQuestSystem() {
    Logger::Info("=== Testing Quest System ===");

    auto& engine = GameEngine::GetInstance();
    auto* questManager = engine.GetQuestManager();

    if (!questManager) {
        Logger::Error("QuestManager not available");
        return;
    }

    // 创建测试玩家
    auto player = CreateTestPlayer("QuestPlayer");

    // 测试获取可用任务
    auto availableQuests = questManager->GetAvailableQuests(player.get(), "新手导师");
    Logger::Info("Available quests: " + std::to_string(availableQuests.size()));

    if (!availableQuests.empty()) {
        WORD questId = availableQuests[0];

        // 测试接受任务
        bool accepted = questManager->AcceptQuest(player.get(), questId);
        Logger::Info("Quest accepted: " + std::string(accepted ? "SUCCESS" : "FAILED"));

        if (accepted) {
            // 测试任务进度更新
            questManager->OnMonsterKilled(player.get(), "鸡");
            questManager->OnMonsterKilled(player.get(), "鸡");
            questManager->OnMonsterKilled(player.get(), "鸡");
            Logger::Info("Quest progress updated");

            // 检查任务状态
            auto playerQuests = questManager->GetPlayerQuests(player.get());
            if (!playerQuests.empty()) {
                auto& quest = playerQuests[0];
                Logger::Info("Quest state: " + std::to_string(static_cast<int>(quest.state)));

                if (!quest.objectives.empty()) {
                    auto& objective = quest.objectives[0];
                    Logger::Info("Objective progress: " + std::to_string(objective.currentCount) +
                                "/" + std::to_string(objective.requiredCount));
                }
            }
        }
    }

    // 获取统计信息
    auto stats = questManager->GetStatistics();
    Logger::Info("Quest Statistics - Total: " + std::to_string(stats.totalQuests) +
                 ", Active: " + std::to_string(stats.activeQuests));
}

// 测试小地图系统
void TestMiniMapSystem() {
    Logger::Info("=== Testing MiniMap System ===");

    auto& engine = GameEngine::GetInstance();
    auto* miniMapManager = engine.GetMiniMapManager();

    if (!miniMapManager) {
        Logger::Error("MiniMapManager not available");
        return;
    }

    // 创建测试玩家
    auto player = CreateTestPlayer("MapPlayer");

    // 测试加载小地图数据
    bool loaded = miniMapManager->LoadMiniMapData("TestMap");
    Logger::Info("MiniMap loaded: " + std::string(loaded ? "SUCCESS" : "FAILED"));

    if (loaded) {
        // 测试发送小地图给玩家
        bool sent = miniMapManager->SendMiniMapToPlayer(player.get(), "TestMap");
        Logger::Info("MiniMap sent: " + std::string(sent ? "SUCCESS" : "FAILED"));

        // 测试添加地图标记
        MapMark testMark;
        testMark.type = MapMarkType::LANDMARK;
        testMark.position = {50, 50};
        testMark.name = "测试地标";
        testMark.description = "这是一个测试地标";

        miniMapManager->AddMapMark("TestMap", testMark);
        Logger::Info("Map mark added");

        // 测试获取地图标记
        auto marks = miniMapManager->GetMapMarks("TestMap", MapMarkType::LANDMARK);
        Logger::Info("Map marks found: " + std::to_string(marks.size()));
    }

    // 获取统计信息
    auto stats = miniMapManager->GetStatistics();
    Logger::Info("MiniMap Statistics - Total Maps: " + std::to_string(stats.totalMaps) +
                 ", Generated: " + std::to_string(stats.generatedMaps));
}

// 主测试函数
int main() {
    Logger::Info("=== GameEngine Core Features Integration Test ===");

    try {
        // 初始化GameEngine
        auto& engine = GameEngine::GetInstance();
        if (!engine.Initialize()) {
            Logger::Error("Failed to initialize GameEngine");
            return 1;
        }

        Logger::Info("GameEngine initialized successfully");

        // 运行各个系统的测试
        TestStorageSystem();
        TestTradeSystem();
        TestQuestSystem();
        TestMiniMapSystem();

        Logger::Info("=== All Tests Completed ===");

        // 清理
        engine.Finalize();

        return 0;

    } catch (const std::exception& e) {
        Logger::Error("Test failed with exception: " + std::string(e.what()));
        return 1;
    }
}
