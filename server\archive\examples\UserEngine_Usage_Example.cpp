// UserEngine_Usage_Example.cpp - UserEngine完善功能使用示例
#include "src/GameEngine/UserEngine.h"
#include "src/GameEngine/MapManager.h"
#include "src/GameEngine/ItemManager.h"
#include "src/GameEngine/MagicManager.h"
#include "src/GameEngine/StorageManager.h"
#include "src/GameEngine/TradeManager.h"
#include "src/GameEngine/QuestManager.h"
#include "src/GameEngine/MiniMapManager.h"
#include "src/GameEngine/RepairManager.h"
#include "src/Common/Logger.h"
#include <iostream>
#include <memory>
#include <thread>
#include <chrono>

using namespace MirServer;

/**
 * UserEngine完善功能使用示例
 * 
 * 本示例展示了如何使用完善后的UserEngine，包括：
 * 1. 正确初始化UserEngine和各个管理器
 * 2. 创建和管理玩家
 * 3. 使用完善的Handle方法处理玩家逻辑
 * 4. 监控和调试玩家状态
 */

class UserEngineExample {
private:
    std::unique_ptr<UserEngine> m_userEngine;
    std::shared_ptr<MapManager> m_mapManager;
    std::shared_ptr<ItemManager> m_itemManager;
    std::shared_ptr<MagicManager> m_magicManager;
    std::shared_ptr<StorageManager> m_storageManager;
    std::shared_ptr<TradeManager> m_tradeManager;
    std::shared_ptr<QuestManager> m_questManager;
    std::shared_ptr<MiniMapManager> m_miniMapManager;
    std::shared_ptr<RepairManager> m_repairManager;

public:
    bool Initialize() {
        std::cout << "=== UserEngine完善功能使用示例 ===" << std::endl;
        
        // 1. 创建所有管理器实例
        m_mapManager = std::make_shared<MapManager>();
        m_itemManager = std::make_shared<ItemManager>();
        m_magicManager = std::make_shared<MagicManager>();
        m_storageManager = std::make_shared<StorageManager>();
        m_tradeManager = std::make_shared<TradeManager>();
        m_questManager = std::make_shared<QuestManager>();
        m_miniMapManager = std::make_shared<MiniMapManager>();
        m_repairManager = std::make_shared<RepairManager>();
        
        // 2. 初始化各个管理器
        if (!m_mapManager->Initialize("./maps")) {
            std::cout << "MapManager初始化失败" << std::endl;
            return false;
        }
        
        if (!m_itemManager->Initialize("./data")) {
            std::cout << "ItemManager初始化失败" << std::endl;
            return false;
        }
        
        m_magicManager->Initialize();
        m_storageManager->Initialize();
        m_tradeManager->Initialize();
        m_questManager->Initialize();
        m_miniMapManager->Initialize();
        m_repairManager->Initialize();
        
        std::cout << "✓ 所有管理器初始化成功" << std::endl;
        
        // 3. 创建并初始化UserEngine
        m_userEngine = std::make_unique<UserEngine>();
        bool result = m_userEngine->Initialize(
            m_mapManager, m_itemManager, m_magicManager,
            m_storageManager, m_tradeManager, m_questManager,
            m_miniMapManager, m_repairManager
        );
        
        if (result) {
            std::cout << "✓ UserEngine初始化成功" << std::endl;
            return true;
        } else {
            std::cout << "✗ UserEngine初始化失败" << std::endl;
            return false;
        }
    }
    
    void DemonstratePlayerManagement() {
        std::cout << "\n--- 玩家管理示例 ---" << std::endl;
        
        // 创建多个测试玩家
        std::vector<std::shared_ptr<PlayObject>> players;
        
        for (int i = 1; i <= 3; ++i) {
            HumDataInfo humData;
            humData.charName = "Player" + std::to_string(i);
            humData.job = static_cast<JobType>(i % 3); // 轮换职业
            humData.gender = static_cast<GenderType>(i % 2);
            humData.level = 10 + i * 5;
            humData.currentPos = {100 + i * 10, 100 + i * 10};
            humData.mapName = "3";
            humData.gold = 1000 * i;
            humData.abil.HP = 80 + i * 10;
            humData.abil.MP = 60 + i * 10;
            humData.abil.MaxHP = 120 + i * 15;
            humData.abil.MaxMP = 100 + i * 15;
            
            auto player = m_userEngine->CreatePlayer(humData);
            if (player) {
                m_userEngine->AddPlayer(player);
                players.push_back(player);
                std::cout << "✓ 创建玩家: " << player->GetCharName() 
                         << " (等级:" << player->GetLevel() << ")" << std::endl;
            }
        }
        
        std::cout << "当前在线玩家数: " << m_userEngine->GetPlayerCount() << std::endl;
    }
    
    void DemonstrateHandleMethods() {
        std::cout << "\n--- Handle方法处理示例 ---" << std::endl;
        
        // 模拟服务器主循环，展示Handle方法的工作
        for (int cycle = 1; cycle <= 5; ++cycle) {
            std::cout << "\n第" << cycle << "个处理周期:" << std::endl;
            
            // 调用ProcessPlayers，这会触发所有Handle方法
            auto startTime = std::chrono::high_resolution_clock::now();
            m_userEngine->ProcessPlayers();
            auto endTime = std::chrono::high_resolution_clock::now();
            
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
            std::cout << "  - ProcessPlayers执行时间: " << duration.count() << "μs" << std::endl;
            
            // 显示处理的Handle方法
            std::cout << "  - 已处理的Handle方法:" << std::endl;
            std::cout << "    * HandlePlayerItems (物品处理)" << std::endl;
            std::cout << "    * HandlePlayerMovement (移动处理)" << std::endl;
            std::cout << "    * HandlePlayerCombat (战斗处理)" << std::endl;
            std::cout << "    * HandlePlayerMagic (魔法处理)" << std::endl;
            std::cout << "    * HandlePlayerTrade (交易处理)" << std::endl;
            std::cout << "    * HandlePlayerQuest (任务处理)" << std::endl;
            std::cout << "    * HandlePlayerStorage (仓库处理)" << std::endl;
            std::cout << "    * HandlePlayerRepair (修理处理)" << std::endl;
            std::cout << "    * UpdatePlayerEnvironment (环境更新)" << std::endl;
            
            // 模拟处理间隔
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
    
    void DemonstratePlayerStatus() {
        std::cout << "\n--- 玩家状态监控示例 ---" << std::endl;
        
        // 获取所有在线玩家并显示状态
        auto players = m_userEngine->GetAllPlayers();
        for (const auto& player : players) {
            std::cout << "\n玩家: " << player->GetCharName() << std::endl;
            std::cout << "  - 生命值: " << player->GetHP() << "/" << player->GetMaxHP() << std::endl;
            std::cout << "  - 魔法值: " << player->GetMP() << "/" << player->GetMaxMP() << std::endl;
            std::cout << "  - 等级: " << player->GetLevel() << std::endl;
            std::cout << "  - 金币: " << player->GetGold() << std::endl;
            std::cout << "  - PK值: " << player->GetPKPoint() << std::endl;
            std::cout << "  - 位置: (" << player->GetCurrentPos().x 
                     << ", " << player->GetCurrentPos().y << ")" << std::endl;
            std::cout << "  - 地图: " << player->GetMapName() << std::endl;
        }
    }
    
    void DemonstrateSpecialFeatures() {
        std::cout << "\n--- 特殊功能示例 ---" << std::endl;
        
        auto players = m_userEngine->GetAllPlayers();
        if (!players.empty()) {
            auto player = players[0];
            
            // 演示GM命令
            std::cout << "执行GM命令示例:" << std::endl;
            std::vector<std::string> levelArgs = {"30"};
            m_userEngine->ProcessGMCommand(player, "LEVEL", levelArgs);
            std::cout << "  - 设置玩家等级为30" << std::endl;
            
            std::vector<std::string> goldArgs = {"10000"};
            m_userEngine->ProcessGMCommand(player, "GOLD", goldArgs);
            std::cout << "  - 设置玩家金币为10000" << std::endl;
            
            // 显示更新后的状态
            std::cout << "更新后状态:" << std::endl;
            std::cout << "  - 等级: " << player->GetLevel() << std::endl;
            std::cout << "  - 金币: " << player->GetGold() << std::endl;
        }
    }
    
    void Cleanup() {
        std::cout << "\n--- 清理资源 ---" << std::endl;
        
        // 移除所有玩家
        auto players = m_userEngine->GetAllPlayers();
        for (const auto& player : players) {
            m_userEngine->RemovePlayer(player->GetCharName());
            std::cout << "✓ 移除玩家: " << player->GetCharName() << std::endl;
        }
        
        std::cout << "✓ 资源清理完成" << std::endl;
    }
    
    void Run() {
        if (!Initialize()) {
            std::cout << "初始化失败，退出示例" << std::endl;
            return;
        }
        
        DemonstratePlayerManagement();
        DemonstrateHandleMethods();
        DemonstratePlayerStatus();
        DemonstrateSpecialFeatures();
        Cleanup();
        
        std::cout << "\n=== UserEngine完善功能示例完成 ===" << std::endl;
        std::cout << "\n总结:" << std::endl;
        std::cout << "1. 成功初始化了UserEngine和所有管理器" << std::endl;
        std::cout << "2. 演示了玩家的创建、管理和移除" << std::endl;
        std::cout << "3. 展示了完善的Handle方法处理流程" << std::endl;
        std::cout << "4. 监控了玩家状态的实时更新" << std::endl;
        std::cout << "5. 演示了GM命令和特殊功能" << std::endl;
        std::cout << "6. 完成了资源的正确清理" << std::endl;
    }
};

int main() {
    try {
        UserEngineExample example;
        example.Run();
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "示例运行异常: " << e.what() << std::endl;
        return 1;
    }
}
