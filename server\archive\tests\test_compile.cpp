#include "../../src/Common/Types.h"
#include "../../src/Common/Utils.h"
#include "../../src/Common/Logger.h"
#include <iostream>

int main() {
    std::cout << "Testing compilation..." << std::endl;
    
    // 测试 Types.h 中的结构
    MirServer::TAbilityRange range(10, 20);
    std::cout << "Range: " << range.min << " - " << range.max << std::endl;
    
    // 测试 Utils.h 中的函数
    std::string test = MirServer::Trim("  hello world  ");
    std::cout << "Trimmed: '" << test << "'" << std::endl;
    
    // 测试 Logger
    MirServer::Logger::GetInstance().Initialize("test.log");
    MirServer::Logger::GetInstance().LogInfo("Test message");
    
    std::cout << "Compilation test successful!" << std::endl;
    return 0;
}
