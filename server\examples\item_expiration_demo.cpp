// item_expiration_demo.cpp - 物品过期系统演示
#include <iostream>
#include <memory>
#include <cstring>
#include <chrono>
#include "../src/GameEngine/UserEngine.h"
#include "../src/GameEngine/ItemManager.h"
#include "../src/BaseObject/PlayObject.h"
#include "../src/Common/Logger.h"

using namespace MirServer;

// 辅助函数：获取当前时间戳
DWORD GetCurrentTime() {
    return static_cast<DWORD>(std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count());
}

// 辅助函数：创建测试物品
UserItem CreateTestItem(int itemIndex, const std::string& name, DWORD expireTime = 0, int expireOffset = 8) {
    UserItem item;
    item.itemIndex = itemIndex;
    item.itemName = name;
    item.makeIndex = 1000 + itemIndex;
    item.dura = 100;
    item.duraMax = 100;
    memset(item.btValue, 0, sizeof(item.btValue));

    if (expireTime > 0) {
        *reinterpret_cast<DWORD*>(&item.btValue[expireOffset]) = expireTime;
    }

    return item;
}

// 辅助函数：创建测试StdItem
std::unique_ptr<StdItem> CreateTestStdItem(int idx, const std::string& name, int source = 0, int stdMode = 5) {
    auto stdItem = std::make_unique<StdItem>();
    stdItem->idx = idx;
    stdItem->name = name;
    stdItem->source = source;
    stdItem->stdMode = stdMode;
    return stdItem;
}

int main() {
    std::cout << "=== 物品过期系统演示 ===" << std::endl;
    std::cout << "本演示展示了修正后的物品过期检查逻辑，遵循原项目实现模式" << std::endl << std::endl;

    // 初始化系统
    Logger::Initialize("item_expiration_demo.log", LogLevel::INFO);

    auto itemManager = std::make_unique<ItemManager>();
    itemManager->Initialize();

    auto userEngine = std::make_unique<UserEngine>();
    userEngine->Initialize(nullptr, itemManager.get());

    // 创建测试玩家
    HumDataInfo humData;
    humData.charName = "DemoPlayer";
    humData.job = JobType::WARRIOR;
    humData.level = 10;
    auto player = userEngine->CreatePlayer(humData);

    DWORD currentTime = GetCurrentTime();
    std::cout << "当前时间戳: " << currentTime << std::endl << std::endl;

    // 测试1: 普通物品（不会过期）
    std::cout << "=== 测试1: 普通物品 ===" << std::endl;
    auto normalStdItem = CreateTestStdItem(1001, "普通剑", 0, 5);
    UserItem normalItem = CreateTestItem(1001, "普通剑");

    DWORD expireTime = 0;
    bool isTimeLimited = userEngine->IsTimeLimitedItem(normalStdItem.get(), &normalItem, expireTime);
    std::cout << "物品: " << normalItem.itemName << std::endl;
    std::cout << "是否限时: " << (isTimeLimited ? "是" : "否") << std::endl;
    std::cout << "过期时间: " << expireTime << std::endl << std::endl;

    // 测试2: GM物品（source=99，限时物品）
    std::cout << "=== 测试2: GM物品 ===" << std::endl;
    auto gmStdItem = CreateTestStdItem(1002, "GM武器", 99, 5);
    UserItem gmItem = CreateTestItem(1002, "GM武器", currentTime + 3600000, 8); // 1小时后过期

    expireTime = 0;
    isTimeLimited = userEngine->IsTimeLimitedItem(gmStdItem.get(), &gmItem, expireTime);
    std::cout << "物品: " << gmItem.itemName << std::endl;
    std::cout << "是否限时: " << (isTimeLimited ? "是" : "否") << std::endl;
    std::cout << "过期时间: " << expireTime << std::endl;
    std::cout << "剩余时间: " << (expireTime > currentTime ? (expireTime - currentTime) / 1000 : 0) << " 秒" << std::endl << std::endl;

    // 测试3: 临时装备（stdMode 50-59）
    std::cout << "=== 测试3: 临时装备 ===" << std::endl;
    auto tempStdItem = CreateTestStdItem(1003, "临时护甲", 0, 55);
    UserItem tempItem = CreateTestItem(1003, "临时护甲", currentTime + 1800000, 10); // 30分钟后过期

    expireTime = 0;
    isTimeLimited = userEngine->IsTimeLimitedItem(tempStdItem.get(), &tempItem, expireTime);
    std::cout << "物品: " << tempItem.itemName << std::endl;
    std::cout << "是否限时: " << (isTimeLimited ? "是" : "否") << std::endl;
    std::cout << "过期时间: " << expireTime << std::endl;
    std::cout << "剩余时间: " << (expireTime > currentTime ? (expireTime - currentTime) / 1000 : 0) << " 秒" << std::endl << std::endl;

    // 测试4: 通过btValue[13]标志的限时物品
    std::cout << "=== 测试4: 标志位限时物品 ===" << std::endl;
    auto flagStdItem = CreateTestStdItem(1004, "特殊戒指", 0, 10);
    UserItem flagItem = CreateTestItem(1004, "特殊戒指", currentTime + 7200000, 9); // 2小时后过期
    flagItem.btValue[13] = 1; // 设置限时标志

    expireTime = 0;
    isTimeLimited = userEngine->IsTimeLimitedItem(flagStdItem.get(), &flagItem, expireTime);
    std::cout << "物品: " << flagItem.itemName << std::endl;
    std::cout << "是否限时: " << (isTimeLimited ? "是" : "否") << std::endl;
    std::cout << "过期时间: " << expireTime << std::endl;
    std::cout << "剩余时间: " << (expireTime > currentTime ? (expireTime - currentTime) / 1000 : 0) << " 秒" << std::endl << std::endl;

    // 测试5: 通过名称识别的限时物品
    std::cout << "=== 测试5: 名称标识限时物品 ===" << std::endl;
    auto activityStdItem = CreateTestStdItem(1005, "活动奖励", 0, 15);
    UserItem activityItem = CreateTestItem(1005, "活动奖励(限时)", currentTime + 900000, 6); // 15分钟后过期

    expireTime = 0;
    isTimeLimited = userEngine->IsTimeLimitedItem(activityStdItem.get(), &activityItem, expireTime);
    std::cout << "物品: " << activityItem.itemName << std::endl;
    std::cout << "是否限时: " << (isTimeLimited ? "是" : "否") << std::endl;
    std::cout << "过期时间: " << expireTime << std::endl;
    std::cout << "剩余时间: " << (expireTime > currentTime ? (expireTime - currentTime) / 1000 : 0) << " 秒" << std::endl << std::endl;

    // 测试6: 已过期物品
    std::cout << "=== 测试6: 已过期物品 ===" << std::endl;
    auto expiredStdItem = CreateTestStdItem(1006, "过期药水", 99, 5);
    UserItem expiredItem = CreateTestItem(1006, "过期药水", currentTime - 1000, 8); // 1秒前就过期了

    expireTime = 0;
    isTimeLimited = userEngine->IsTimeLimitedItem(expiredStdItem.get(), &expiredItem, expireTime);
    std::cout << "物品: " << expiredItem.itemName << std::endl;
    std::cout << "是否限时: " << (isTimeLimited ? "是" : "否") << std::endl;
    std::cout << "过期时间: " << expireTime << std::endl;
    std::cout << "是否已过期: " << (currentTime >= expireTime ? "是" : "否") << std::endl << std::endl;

    // 测试7: 永不过期物品（expireTime=0）
    std::cout << "=== 测试7: 永不过期物品 ===" << std::endl;
    auto permanentStdItem = CreateTestStdItem(1007, "永久装备", 99, 5);
    UserItem permanentItem = CreateTestItem(1007, "永久装备", 0, 8); // expireTime=0表示永不过期

    expireTime = 0;
    isTimeLimited = userEngine->IsTimeLimitedItem(permanentStdItem.get(), &permanentItem, expireTime);
    std::cout << "物品: " << permanentItem.itemName << std::endl;
    std::cout << "是否限时: " << (isTimeLimited ? "是" : "否") << std::endl;
    std::cout << "过期时间: " << expireTime << std::endl;
    std::cout << "是否永不过期: " << (expireTime == 0 ? "是" : "否") << std::endl << std::endl;

    // 演示完整的过期检查流程
    std::cout << "=== 完整过期检查演示 ===" << std::endl;

    // 向玩家背包添加测试物品
    auto& bagItems = const_cast<std::vector<UserItem>&>(player->GetBagItems());
    bagItems.clear();
    bagItems.push_back(normalItem);
    bagItems.push_back(gmItem);
    bagItems.push_back(tempItem);
    bagItems.push_back(expiredItem); // 这个会被移除
    bagItems.push_back(permanentItem);

    std::cout << "检查前背包物品数量: " << bagItems.size() << std::endl;

    // 执行过期检查
    userEngine->CheckItemExpiration(player, currentTime);

    std::cout << "检查后背包物品数量: " << bagItems.size() << std::endl;
    std::cout << "剩余物品:" << std::endl;
    for (const auto& item : bagItems) {
        std::cout << "  - " << item.itemName << std::endl;
    }

    std::cout << std::endl << "=== 演示完成 ===" << std::endl;
    std::cout << "修正后的物品过期系统特点:" << std::endl;
    std::cout << "1. 遵循原项目实现模式" << std::endl;
    std::cout << "2. 支持多种限时物品识别方式" << std::endl;
    std::cout << "3. 基于真实时间的过期检查" << std::endl;
    std::cout << "4. 正确处理永不过期物品" << std::endl;
    std::cout << "5. 高效的批量检查算法" << std::endl;
    std::cout << "6. 过期装备直接消失（不进入背包）" << std::endl;
    std::cout << "7. 过期背包物品直接移除" << std::endl;

    // 清理资源
    userEngine.reset();
    itemManager.reset();
    Logger::Finalize();

    return 0;
}
