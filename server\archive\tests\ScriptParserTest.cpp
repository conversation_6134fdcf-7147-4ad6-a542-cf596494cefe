#include "../src/GameEngine/ScriptEngine.h"
#include "../src/Common/Logger.h"
#include <iostream>
#include <cassert>
#include <filesystem>

using namespace MirServer;

void TestScriptParser() {
    Logger::Info("开始测试脚本解析器...");

    ScriptParser parser;
    NPCScript script;

    // 测试解析脚本文件
    std::string scriptFile = "../scripts/test_npc.txt";
    if (!std::filesystem::exists(scriptFile)) {
        scriptFile = "scripts/test_npc.txt";
    }
    bool result = parser.ParseScriptFile(scriptFile, script);

    if (!result) {
        Logger::Error("脚本解析失败: " + parser.GetLastError());
        return;
    }

    Logger::Info("脚本解析成功！");
    Logger::Info("脚本文件: " + script.scriptFile);
    Logger::Info("脚本块数量: " + std::to_string(script.blocks.size()));

    // 测试各个脚本块
    const ScriptBlock* mainBlock = script.GetBlock("main");
    assert(mainBlock != nullptr);
    Logger::Info("[@main] 块解析成功");
    Logger::Info("对话文本: " + mainBlock->text);
    Logger::Info("选项数量: " + std::to_string(mainBlock->options.size()));

    // 测试选项解析
    for (const auto& option : mainBlock->options) {
        Logger::Info("选项: " + option.text + " -> " + option.gotoLabel);
    }

    // 测试shop块
    const ScriptBlock* shopBlock = script.GetBlock("shop");
    assert(shopBlock != nullptr);
    Logger::Info("[@shop] 块解析成功");
    Logger::Info("条件数量: " + std::to_string(shopBlock->conditions.size()));
    Logger::Info("动作数量: " + std::to_string(shopBlock->actions.size()));
    Logger::Info("ELSE动作数量: " + std::to_string(shopBlock->elseActions.size()));

    // 测试条件解析
    if (!shopBlock->conditions.empty()) {
        const auto& condition = shopBlock->conditions[0];
        Logger::Info("条件类型: " + condition.type);
        Logger::Info("条件参数1: " + condition.param1);
        Logger::Info("条件参数2: " + condition.param2);
        Logger::Info("数值参数1: " + std::to_string(condition.nParam1));
    }

    // 测试动作解析
    if (!shopBlock->actions.empty()) {
        const auto& action = shopBlock->actions[0];
        Logger::Info("动作类型: " + action.type);
        Logger::Info("动作参数1: " + action.param1);
        Logger::Info("动作参数2: " + action.param2);
    }

    // 测试quest块
    const ScriptBlock* questBlock = script.GetBlock("quest");
    assert(questBlock != nullptr);
    Logger::Info("[@quest] 块解析成功");

    // 测试工具方法
    Logger::Info("测试工具方法...");

    // 测试Trim
    assert(ScriptParser::Trim("  hello  ") == "hello");
    assert(ScriptParser::Trim("") == "");
    assert(ScriptParser::Trim("   ") == "");

    // 测试Split
    auto parts = ScriptParser::Split("a,b,c", ',');
    assert(parts.size() == 3);
    assert(parts[0] == "a");
    assert(parts[1] == "b");
    assert(parts[2] == "c");

    // 测试SplitParams
    auto params = ScriptParser::SplitParams("CHECKLEVEL > 10");
    assert(params.size() == 3);
    assert(params[0] == "CHECKLEVEL");
    assert(params[1] == ">");
    assert(params[2] == "10");

    // 测试IsLabel
    assert(ScriptParser::IsLabel("[@main]") == true);
    assert(ScriptParser::IsLabel("[@shop]") == true);
    assert(ScriptParser::IsLabel("[main]") == false);
    assert(ScriptParser::IsLabel("main") == false);

    // 测试IsConditionSection
    assert(ScriptParser::IsConditionSection("#IF") == true);
    assert(ScriptParser::IsConditionSection("#if") == true);
    assert(ScriptParser::IsConditionSection("IF") == false);

    // 测试IsActionSection
    assert(ScriptParser::IsActionSection("#ACT") == true);
    assert(ScriptParser::IsActionSection("#act") == true);
    assert(ScriptParser::IsActionSection("ACT") == false);

    // 测试IsElseActionSection
    assert(ScriptParser::IsElseActionSection("#ELSEACT") == true);
    assert(ScriptParser::IsElseActionSection("#elseact") == true);
    assert(ScriptParser::IsElseActionSection("ELSEACT") == false);

    // 测试IsOption
    assert(ScriptParser::IsOption("<购买物品/@shop>") == true);
    assert(ScriptParser::IsOption("<离开/@exit>") == true);
    assert(ScriptParser::IsOption("购买物品") == false);

    // 测试IsComment
    assert(ScriptParser::IsComment("; 这是注释") == true);
    assert(ScriptParser::IsComment("// 这是注释") == true);
    assert(ScriptParser::IsComment("/ 这不是注释") == false);

    // 测试ExtractLabel
    assert(ScriptParser::ExtractLabel("[@main]") == "main");
    assert(ScriptParser::ExtractLabel("[@shop]") == "shop");
    assert(ScriptParser::ExtractLabel("[main]") == "");

    // 测试ExtractOptionText
    assert(ScriptParser::ExtractOptionText("<购买物品/@shop>") == "购买物品");
    assert(ScriptParser::ExtractOptionText("<离开/@exit>") == "离开");

    // 测试ExtractOptionGoto
    assert(ScriptParser::ExtractOptionGoto("<购买物品/@shop>") == "shop");
    assert(ScriptParser::ExtractOptionGoto("<离开/@exit>") == "exit");

    Logger::Info("所有测试通过！");
}

void TestVariableReplacement() {
    Logger::Info("测试变量替换功能...");

    // 由于需要PlayObject和NPC对象，这里只测试基本的替换逻辑
    std::string text = "你好，<$USERNAME>！你是<$LEVEL>级的<$JOB>。";

    // 这里应该传入真实的PlayObject对象，但为了测试，我们只验证函数不会崩溃
    std::string result = ScriptParser::ReplaceVariables(text, nullptr, nullptr);
    assert(result == text); // 没有player时应该返回原文本

    Logger::Info("变量替换测试完成");
}

void TestConditionValidation() {
    Logger::Info("测试条件验证...");

    ScriptParser parser;

    // 测试有效条件
    ScriptCondition validCondition("CHECKLEVEL", ">", "10");
    assert(parser.ValidateCondition(validCondition) == true);

    ScriptCondition validCondition2("CHECKITEM", "金创药", "5");
    assert(parser.ValidateCondition(validCondition2) == true);

    // 测试无效条件
    ScriptCondition invalidCondition("INVALID_CONDITION");
    assert(parser.ValidateCondition(invalidCondition) == false);

    Logger::Info("条件验证测试完成");
}

void TestActionValidation() {
    Logger::Info("测试动作验证...");

    ScriptParser parser;

    // 测试有效动作
    ScriptAction validAction("GIVE", "金创药", "5");
    assert(parser.ValidateAction(validAction) == true);

    ScriptAction validAction2("SENDMSG", "5", "欢迎光临！");
    assert(parser.ValidateAction(validAction2) == true);

    // 测试无效动作
    ScriptAction invalidAction("INVALID_ACTION");
    assert(parser.ValidateAction(invalidAction) == false);

    Logger::Info("动作验证测试完成");
}

int main() {
    try {
        TestScriptParser();
        TestVariableReplacement();
        TestConditionValidation();
        TestActionValidation();

        std::cout << "所有脚本解析器测试通过！" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}
