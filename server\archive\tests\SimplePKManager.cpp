#include "SimplePKManager.h"
#include <algorithm>

// 使用Windows的GetTickCount函数
#ifdef _WIN32
#include <windows.h>
#else
#include <chrono>
// 非Windows平台的GetTickCount实现
static MirServer::DWORD GetTickCount() {
    auto now = std::chrono::steady_clock::now();
    auto duration = now.time_since_epoch();
    auto millis = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
    return static_cast<MirServer::DWORD>(millis);
}
#endif

namespace MirServer {

SimplePKManager& SimplePKManager::GetInstance() {
    static SimplePKManager instance;
    return instance;
}

void SimplePKManager::Initialize() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized) {
        return;
    }

    m_lastUpdateTime = ::GetTickCount();
    m_initialized = true;
}

void SimplePKManager::Finalize() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return;
    }

    m_guildWars.clear();
    m_initialized = false;
}

bool SimplePKManager::StartGuildWar(const std::string& guild1, const std::string& guild2, DWORD duration) {
    if (guild1.empty() || guild2.empty() || guild1 == guild2) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查是否已经在战争中（不调用IsGuildWar避免重复加锁）
    auto it = std::find_if(m_guildWars.begin(), m_guildWars.end(),
        [&](const GuildWarInfo& war) {
            return war.isActive &&
                   ((war.guild1 == guild1 && war.guild2 == guild2) ||
                    (war.guild1 == guild2 && war.guild2 == guild1));
        });

    if (it != m_guildWars.end()) {
        return false;
    }

    // 创建新的行会战
    GuildWarInfo warInfo;
    warInfo.guild1 = guild1;
    warInfo.guild2 = guild2;
    warInfo.startTime = ::GetTickCount();
    warInfo.duration = duration;
    warInfo.isActive = true;

    m_guildWars.push_back(warInfo);

    return true;
}

bool SimplePKManager::EndGuildWar(const std::string& guild1, const std::string& guild2) {
    if (guild1.empty() || guild2.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_guildWars.begin(), m_guildWars.end(),
        [&](const GuildWarInfo& war) {
            return war.isActive &&
                   ((war.guild1 == guild1 && war.guild2 == guild2) ||
                    (war.guild1 == guild2 && war.guild2 == guild1));
        });

    if (it != m_guildWars.end()) {
        it->isActive = false;
        return true;
    }

    return false;
}

bool SimplePKManager::IsGuildWar(const std::string& guild1, const std::string& guild2) const {
    if (guild1.empty() || guild2.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_guildWars.begin(), m_guildWars.end(),
        [&](const GuildWarInfo& war) {
            return war.isActive &&
                   ((war.guild1 == guild1 && war.guild2 == guild2) ||
                    (war.guild1 == guild2 && war.guild2 == guild1));
        });

    return it != m_guildWars.end();
}

std::vector<GuildWarInfo> SimplePKManager::GetActiveGuildWars() const {
    std::lock_guard<std::mutex> lock(m_mutex);

    std::vector<GuildWarInfo> activeWars;
    for (const auto& war : m_guildWars) {
        if (war.isActive) {
            activeWars.push_back(war);
        }
    }

    return activeWars;
}

void SimplePKManager::Update() {
    if (!m_initialized) {
        return;
    }

    DWORD currentTime = ::GetTickCount();

    // 每秒更新一次
    if (currentTime - m_lastUpdateTime >= 1000) {
        std::lock_guard<std::mutex> lock(m_mutex);

        // 处理行会战
        ProcessGuildWars();

        m_lastUpdateTime = currentTime;
    }
}

void SimplePKManager::ProcessGuildWars() {
    DWORD currentTime = ::GetTickCount();

    // 检查行会战是否超时
    for (auto& war : m_guildWars) {
        if (war.isActive) {
            DWORD elapsedTime = (currentTime - war.startTime) / 1000 / 60; // 转换为分钟

            if (elapsedTime >= war.duration) {
                war.isActive = false;
            }
        }
    }

    // 清理过期的行会战记录
    m_guildWars.erase(
        std::remove_if(m_guildWars.begin(), m_guildWars.end(),
            [currentTime](const GuildWarInfo& war) {
                return !war.isActive &&
                       (currentTime - war.startTime) > 24 * 60 * 60 * 1000; // 24小时后清理
            }),
        m_guildWars.end()
    );
}

} // namespace MirServer
