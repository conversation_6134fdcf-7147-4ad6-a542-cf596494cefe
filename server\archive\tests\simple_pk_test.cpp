#include "src/GameEngine/PKManager.h"
#include "src/GameEngine/GuildManager.h"
#include "src/Common/Types.h"
#include "src/BaseObject/BaseObject.h"
#include <iostream>
#include <memory>

using namespace MirServer;

// 简化的PlayObject类用于测试
class TestPlayObject {
private:
    std::string m_charName;
    MirServer::DWORD m_pkPoint = 0;
    AttackMode m_attackMode = AttackMode::PEACE;

public:
    TestPlayObject(const std::string& name) : m_charName(name) {}

    const std::string& GetCharName() const { return m_charName; }
    void SetCharName(const std::string& name) { m_charName = name; }

    MirServer::DWORD GetPKPoint() const { return m_pkPoint; }
    void IncPKPoint(int value) {
        m_pkPoint += value;
        if (m_pkPoint < 0) m_pkPoint = 0;
    }

    AttackMode GetAttackMode() const { return m_attackMode; }
    void SetAttackMode(AttackMode mode) { m_attackMode = mode; }

    ObjectType GetObjectType() const { return ObjectType::HUMAN; }

    // 简化的组队检查
    bool IsGroupMember(const TestPlayObject* other) const { return false; }

    // 简化的环境检查
    class Environment* GetEnvironment() const { return nullptr; }
};

int main() {
    std::cout << "=== 简化PK系统测试 ===" << std::endl;

    try {
        // 初始化PK管理器
        auto& pkManager = PKManager::GetInstance();
        pkManager.Initialize();

        std::cout << "PK管理器初始化成功" << std::endl;

        // 创建测试玩家
        auto player1 = std::make_unique<TestPlayObject>("TestPlayer1");
        auto player2 = std::make_unique<TestPlayObject>("TestPlayer2");

        std::cout << "创建测试玩家: " << player1->GetCharName() << ", " << player2->GetCharName() << std::endl;

        // 测试1: 配置管理
        std::cout << "\n--- 测试1: 配置管理 ---" << std::endl;
        pkManager.SetKillAddPKValue(100);
        pkManager.SetRedNamePKValue(200);
        pkManager.SetYellowNamePKValue(100);

        std::cout << "杀人增加PK值: " << pkManager.GetKillAddPKValue() << std::endl;
        std::cout << "红名PK值阈值: " << pkManager.GetRedNamePKValue() << std::endl;
        std::cout << "黄名PK值阈值: " << pkManager.GetYellowNamePKValue() << std::endl;

        // 测试2: 行会战系统
        std::cout << "\n--- 测试2: 行会战系统 ---" << std::endl;
        bool warStarted = pkManager.StartGuildWar("TestGuild1", "TestGuild2", 30);
        std::cout << "行会战开始: " << (warStarted ? "成功" : "失败") << std::endl;

        bool isWar = pkManager.IsGuildWar("TestGuild1", "TestGuild2");
        std::cout << "行会战状态: " << (isWar ? "进行中" : "未进行") << std::endl;

        auto activeWars = pkManager.GetActiveGuildWars();
        std::cout << "当前活跃行会战数量: " << activeWars.size() << std::endl;

        if (!activeWars.empty()) {
            const auto& war = activeWars[0];
            std::cout << "行会战详情: " << war.guild1 << " vs " << war.guild2
                     << " (持续" << war.duration << "分钟)" << std::endl;
        }

        bool warEnded = pkManager.EndGuildWar("TestGuild1", "TestGuild2");
        std::cout << "行会战结束: " << (warEnded ? "成功" : "失败") << std::endl;

        // 测试3: 重复开始行会战（应该失败）
        std::cout << "\n--- 测试3: 重复行会战 ---" << std::endl;
        pkManager.StartGuildWar("Guild1", "Guild2", 15);
        bool duplicateWar = pkManager.StartGuildWar("Guild1", "Guild2", 20);
        std::cout << "重复开始行会战: " << (duplicateWar ? "成功（错误）" : "失败（正确）") << std::endl;

        // 测试4: 不存在的行会战结束
        std::cout << "\n--- 测试4: 结束不存在的行会战 ---" << std::endl;
        bool endNonExistent = pkManager.EndGuildWar("NonExistent1", "NonExistent2");
        std::cout << "结束不存在的行会战: " << (endNonExistent ? "成功（错误）" : "失败（正确）") << std::endl;

        // 测试5: 更新机制
        std::cout << "\n--- 测试5: 更新机制 ---" << std::endl;
        pkManager.Update();
        std::cout << "PK管理器更新完成" << std::endl;

        // 清理
        pkManager.Finalize();

        std::cout << "\n=== 简化PK系统测试完成 ===" << std::endl;
        std::cout << "所有测试通过!" << std::endl;

        return 0;

    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}
