// EnvironmentTest.cpp - Environment功能测试
#include "Environment.h"
#include "../Common/Logger.h"
#include <iostream>
#include <cassert>

namespace MirServer {

// 测试Environment新功能
void TestEnvironmentFeatures() {
    Logger::Info("开始测试Environment新功能...");
    
    // 创建测试环境
    Environment env("测试地图", 100, 100);
    
    // 测试基本信息设置
    env.SetMapDesc("这是一个测试地图");
    env.SetMainMapName("主地图");
    env.SetSubMapName("子地图");
    env.SetServerIndex(1);
    env.SetRequestLevel(10);
    env.SetMinMap(1);
    env.SetMainMap(true);
    
    assert(env.GetMapDesc() == "这是一个测试地图");
    assert(env.GetMainMapName() == "主地图");
    assert(env.GetSubMapName() == "子地图");
    assert(env.GetServerIndex() == 1);
    assert(env.GetRequestLevel() == 10);
    assert(env.GetMinMap() == 1);
    assert(env.IsMainMap() == true);
    
    Logger::Info("✓ 基本信息设置测试通过");
    
    // 测试地图标志设置
    MapFlags flags;
    flags.isSafe = true;
    flags.isFightZone = false;
    flags.isFight3Zone = true;
    flags.isDark = true;
    flags.isDay = false;
    flags.isQuiz = true;
    flags.noReconnect = true;
    flags.needHole = false;
    flags.noRecall = true;
    flags.noGuildRecall = true;
    flags.noDearRecall = false;
    flags.noMasterRecall = true;
    flags.noRandomMove = true;
    flags.noDrug = false;
    flags.isMine = true;
    flags.noPositionMove = true;
    flags.runHuman = true;
    flags.runMonster = false;
    flags.incHP = true;
    flags.decHP = false;
    flags.incGameGold = true;
    flags.decGameGold = false;
    flags.incGamePoint = true;
    flags.decGamePoint = false;
    flags.hasMusic = true;
    flags.expRate = true;
    flags.pkWinLevel = true;
    flags.pkWinExp = false;
    flags.pkLostLevel = true;
    flags.pkLostExp = false;
    flags.noFireMagic = true;
    flags.unAllowStdItems = true;
    
    // 设置数值参数
    flags.pkWinLevelValue = 1;
    flags.pkLostLevelValue = 1;
    flags.pkWinExpValue = 1000;
    flags.pkLostExpValue = 500;
    flags.decHPTime = 5000;
    flags.decHPPoint = 10;
    flags.incHPTime = 3000;
    flags.incHPPoint = 20;
    flags.decGameGoldTime = 10000;
    flags.decGameGoldValue = 100;
    flags.incGameGoldTime = 8000;
    flags.incGameGoldValue = 50;
    flags.decGamePointTime = 15000;
    flags.decGamePointValue = 5;
    flags.incGamePointTime = 12000;
    flags.incGamePointValue = 10;
    flags.musicID = 123;
    flags.expRateValue = 200;
    flags.noReconnectMap = "比奇城";
    flags.unAllowStdItemsText = "屠龙|倚天剑|麻痹戒指";
    
    env.SetMapFlags(flags);
    
    Logger::Info("✓ 地图标志设置测试通过");
    
    // 测试地图区域检查
    Point testPos(50, 50);
    
    assert(env.IsSafeZone(testPos) == true);
    assert(env.IsFightZone(testPos) == false);
    assert(env.IsFight3Zone(testPos) == true);
    assert(env.IsNoReconnectZone(testPos) == true);
    assert(env.IsNoRandomZone(testPos) == true);
    assert(env.IsNoDrugZone(testPos) == false);
    assert(env.IsMineZone(testPos) == true);
    assert(env.IsNoPositionMoveZone(testPos) == true);
    assert(env.IsNoRecallZone(testPos) == true);
    assert(env.IsNoGuildRecallZone(testPos) == true);
    assert(env.IsNoDearRecallZone(testPos) == false);
    assert(env.IsNoMasterRecallZone(testPos) == true);
    assert(env.IsQuizZone(testPos) == true);
    assert(env.IsNeedHoleZone(testPos) == false);
    assert(env.IsDarknessZone(testPos) == true);
    assert(env.IsDaylightZone(testPos) == false);
    assert(env.IsRunHumanZone(testPos) == true);
    assert(env.IsRunMonsterZone(testPos) == false);
    assert(env.IsNoFireMagicZone(testPos) == true);
    
    Logger::Info("✓ 地图区域检查测试通过");
    
    // 测试物品限制检查
    assert(env.AllowStdItems("屠龙") == false);
    assert(env.AllowStdItems("倚天剑") == false);
    assert(env.AllowStdItems("麻痹戒指") == false);
    assert(env.AllowStdItems("普通剑") == true);
    assert(env.AllowStdItems("金创药") == true);
    
    Logger::Info("✓ 物品限制检查测试通过");
    
    // 测试环境信息获取
    std::string envInfo = env.GetEnvironmentInfo();
    assert(!envInfo.empty());
    assert(envInfo.find("测试地图") != std::string::npos);
    assert(envInfo.find("安全区") != std::string::npos);
    assert(envInfo.find("行会战区") != std::string::npos);
    assert(envInfo.find("黑暗") != std::string::npos);
    assert(envInfo.find("答题区") != std::string::npos);
    assert(envInfo.find("音乐区") != std::string::npos);
    assert(envInfo.find("经验倍率: 200%") != std::string::npos);
    
    Logger::Info("✓ 环境信息获取测试通过");
    
    // 输出环境信息
    std::cout << "\n=== 环境信息 ===" << std::endl;
    std::cout << envInfo << std::endl;
    
    Logger::Info("Environment新功能测试全部通过！");
}

} // namespace MirServer

// 主函数用于独立测试
int main() {
    try {
        MirServer::TestEnvironmentFeatures();
        std::cout << "\n所有测试通过！" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}
