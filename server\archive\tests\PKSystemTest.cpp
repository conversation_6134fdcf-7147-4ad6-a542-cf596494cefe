#include "PKManager.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>

using namespace MirServer;

// 简单的测试函数
void TestPKSystem() {
    Logger::Info("开始PK系统测试...");
    
    // 初始化PK管理器
    auto& pkManager = PKManager::GetInstance();
    pkManager.Initialize();
    
    // 创建测试玩家
    auto player1 = std::make_shared<PlayObject>();
    auto player2 = std::make_shared<PlayObject>();
    
    player1->SetCharName("TestPlayer1");
    player2->SetCharName("TestPlayer2");
    
    // 测试PK值设置
    Logger::Info("测试PK值设置...");
    pkManager.SetPKValue(player1.get(), 150);
    DWORD pkValue = pkManager.GetPKValue(player1.get());
    Logger::Info("Player1 PK值: " + std::to_string(pkValue));
    
    // 测试红名/黄名状态
    Logger::Info("测试PK状态判断...");
    bool isYellow = pkManager.IsYellowName(player1.get());
    bool isRed = pkManager.IsRedName(player1.get());
    Logger::Info("Player1 黄名: " + std::string(isYellow ? "是" : "否"));
    Logger::Info("Player1 红名: " + std::string(isRed ? "是" : "否"));
    
    // 测试攻击模式
    Logger::Info("测试攻击模式...");
    pkManager.SetAttackMode(player1.get(), PKMode::ALL);
    PKMode mode = pkManager.GetAttackMode(player1.get());
    Logger::Info("Player1 攻击模式: " + std::to_string(static_cast<int>(mode)));
    
    // 测试攻击判断
    Logger::Info("测试攻击判断...");
    bool canAttack = pkManager.CanAttack(player1.get(), player2.get());
    Logger::Info("Player1 可以攻击 Player2: " + std::string(canAttack ? "是" : "否"));
    
    // 测试行会战
    Logger::Info("测试行会战...");
    bool warStarted = pkManager.StartGuildWar("TestGuild1", "TestGuild2", 30);
    Logger::Info("行会战开始: " + std::string(warStarted ? "成功" : "失败"));
    
    bool isWar = pkManager.IsGuildWar("TestGuild1", "TestGuild2");
    Logger::Info("行会战状态: " + std::string(isWar ? "进行中" : "未进行"));
    
    bool warEnded = pkManager.EndGuildWar("TestGuild1", "TestGuild2");
    Logger::Info("行会战结束: " + std::string(warEnded ? "成功" : "失败"));
    
    // 测试PK事件
    Logger::Info("测试PK事件...");
    pkManager.OnPlayerLogin(player1.get());
    pkManager.OnPlayerLogin(player2.get());
    
    // 模拟杀人事件
    pkManager.OnPlayerKill(player1.get(), player2.get());
    DWORD newPKValue = pkManager.GetPKValue(player1.get());
    Logger::Info("杀人后 Player1 PK值: " + std::to_string(newPKValue));
    
    // 测试PK值衰减
    Logger::Info("测试PK值衰减...");
    pkManager.Update(); // 触发更新
    
    // 清理
    pkManager.OnPlayerLogout(player1.get());
    pkManager.OnPlayerLogout(player2.get());
    pkManager.Finalize();
    
    Logger::Info("PK系统测试完成!");
}

int main() {
    // 初始化日志系统
    Logger::Initialize("PKSystemTest.log");
    
    try {
        TestPKSystem();
        std::cout << "PK系统测试成功完成!" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "PK系统测试失败: " << e.what() << std::endl;
        return 1;
    }
    
    Logger::Finalize();
    return 0;
}
