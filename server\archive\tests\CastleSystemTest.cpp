// CastleSystemTest.cpp - 城堡系统测试
#include <gtest/gtest.h>
#include "../src/GameEngine/CastleManager.h"
#include "../src/GameEngine/GuildManager.h"
#include "../src/Common/Logger.h"
#include <filesystem>

using namespace MirServer;

class CastleSystemTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建测试目录
        std::filesystem::create_directories("test_castle");
        
        // 初始化日志系统
        Logger::Initialize("test_castle.log", LogLevel::INFO);
        
        // 初始化行会管理器（城堡系统依赖）
        GuildManager::GetInstance().Initialize();
        
        // 初始化城堡管理器
        CastleManager::GetInstance().Initialize("test_castle");
    }
    
    void TearDown() override {
        // 清理城堡管理器
        CastleManager::GetInstance().Finalize();
        
        // 清理行会管理器
        GuildManager::GetInstance().Finalize();
        
        // 清理测试目录
        std::filesystem::remove_all("test_castle");
        
        Logger::Finalize();
    }
};

// 测试城堡管理器初始化
TEST_F(CastleSystemTest, CastleManagerInitialization) {
    CastleManager& manager = CastleManager::GetInstance();
    
    // 应该至少有一个默认城堡
    EXPECT_GT(manager.GetCastleCount(), 0);
    
    // 应该能找到默认城堡
    Castle* defaultCastle = manager.FindCastle("沙巴克");
    EXPECT_NE(defaultCastle, nullptr);
    
    if (defaultCastle) {
        EXPECT_EQ(defaultCastle->GetCastleName(), "沙巴克");
        EXPECT_EQ(defaultCastle->GetMapName(), "3");
        EXPECT_EQ(defaultCastle->GetHomeMap(), "3");
        EXPECT_EQ(defaultCastle->GetHomeX(), 644);
        EXPECT_EQ(defaultCastle->GetHomeY(), 290);
    }
}

// 测试城堡基本信息
TEST_F(CastleSystemTest, CastleBasicInfo) {
    Castle* castle = CastleManager::GetInstance().FindCastle("沙巴克");
    ASSERT_NE(castle, nullptr);
    
    // 测试基本信息
    EXPECT_EQ(castle->GetCastleName(), "沙巴克");
    EXPECT_EQ(castle->GetOwnerGuild(), ""); // 初始无拥有者
    EXPECT_EQ(castle->GetWarStatus(), CastleWarStatus::PEACE);
    EXPECT_FALSE(castle->IsUnderAttack());
    
    // 测试经济信息
    EXPECT_EQ(castle->GetTotalGold(), 0);
    EXPECT_EQ(castle->GetTodayIncome(), 0);
    EXPECT_EQ(castle->GetTechLevel(), 0);
    EXPECT_EQ(castle->GetPower(), 0);
}

// 测试攻击者管理
TEST_F(CastleSystemTest, AttackerManagement) {
    Castle* castle = CastleManager::GetInstance().FindCastle("沙巴克");
    ASSERT_NE(castle, nullptr);
    
    DWORD currentTime = GetCurrentTime();
    
    // 添加攻击者
    EXPECT_TRUE(castle->AddAttacker("测试行会1", currentTime));
    EXPECT_TRUE(castle->AddAttacker("测试行会2", currentTime));
    
    // 重复添加应该失败
    EXPECT_FALSE(castle->AddAttacker("测试行会1", currentTime));
    
    // 检查攻击者
    EXPECT_TRUE(castle->IsAttacker("测试行会1"));
    EXPECT_TRUE(castle->IsAttacker("测试行会2"));
    EXPECT_FALSE(castle->IsAttacker("不存在的行会"));
    
    // 获取攻击者列表
    std::string attackerList = castle->GetAttackerList();
    EXPECT_TRUE(attackerList.find("测试行会1") != std::string::npos);
    EXPECT_TRUE(attackerList.find("测试行会2") != std::string::npos);
    
    // 移除攻击者
    EXPECT_TRUE(castle->RemoveAttacker("测试行会1"));
    EXPECT_FALSE(castle->IsAttacker("测试行会1"));
    EXPECT_TRUE(castle->IsAttacker("测试行会2"));
    
    // 重复移除应该失败
    EXPECT_FALSE(castle->RemoveAttacker("测试行会1"));
}

// 测试防御单元修复
TEST_F(CastleSystemTest, DefenseUnitRepair) {
    Castle* castle = CastleManager::GetInstance().FindCastle("沙巴克");
    ASSERT_NE(castle, nullptr);
    
    // 测试修复城门
    EXPECT_TRUE(castle->RepairDoor());
    
    // 测试修复城墙
    EXPECT_TRUE(castle->RepairWall(0)); // 左墙
    EXPECT_TRUE(castle->RepairWall(1)); // 中墙
    EXPECT_TRUE(castle->RepairWall(2)); // 右墙
    EXPECT_FALSE(castle->RepairWall(3)); // 无效索引
    
    // 测试修复特定防御单元
    EXPECT_TRUE(castle->RepairUnit(DefenseUnitType::MAIN_DOOR));
    EXPECT_TRUE(castle->RepairUnit(DefenseUnitType::LEFT_WALL));
    EXPECT_TRUE(castle->RepairUnit(DefenseUnitType::CENTER_WALL));
    EXPECT_TRUE(castle->RepairUnit(DefenseUnitType::RIGHT_WALL));
}

// 测试经济系统
TEST_F(CastleSystemTest, EconomicSystem) {
    Castle* castle = CastleManager::GetInstance().FindCastle("沙巴克");
    ASSERT_NE(castle, nullptr);
    
    // 测试收入
    castle->IncomeGold(1000);
    EXPECT_EQ(castle->GetTotalGold(), 1000);
    EXPECT_EQ(castle->GetTodayIncome(), 1000);
    
    castle->IncomeGold(500);
    EXPECT_EQ(castle->GetTotalGold(), 1500);
    EXPECT_EQ(castle->GetTodayIncome(), 1500);
    
    // TODO: 测试提取和存入金币（需要PlayObject集成）
    // int withdrawn = castle->WithdrawGold(nullptr, 500);
    // EXPECT_EQ(withdrawn, 0); // 无效玩家应该返回0
    
    // int deposited = castle->DepositGold(nullptr, 200);
    // EXPECT_EQ(deposited, 0); // 无效玩家应该返回0
}

// 测试技术和力量系统
TEST_F(CastleSystemTest, TechAndPowerSystem) {
    Castle* castle = CastleManager::GetInstance().FindCastle("沙巴克");
    ASSERT_NE(castle, nullptr);
    
    // 测试技术等级
    castle->SetTechLevel(5);
    EXPECT_EQ(castle->GetTechLevel(), 5);
    
    castle->SetTechLevel(10);
    EXPECT_EQ(castle->GetTechLevel(), 10);
    
    // 测试无效等级
    castle->SetTechLevel(-1);
    EXPECT_EQ(castle->GetTechLevel(), 10); // 应该保持不变
    
    castle->SetTechLevel(15);
    EXPECT_EQ(castle->GetTechLevel(), 10); // 应该保持不变（超过最大值）
    
    // 测试力量值
    castle->SetPower(100);
    EXPECT_EQ(castle->GetPower(), 100);
    
    castle->SetPower(500);
    EXPECT_EQ(castle->GetPower(), 500);
    
    // 测试无效力量值
    castle->SetPower(-10);
    EXPECT_EQ(castle->GetPower(), 500); // 应该保持不变
}

// 测试战争系统
TEST_F(CastleSystemTest, WarSystem) {
    Castle* castle = CastleManager::GetInstance().FindCastle("沙巴克");
    ASSERT_NE(castle, nullptr);
    
    // 初始状态应该是和平
    EXPECT_EQ(castle->GetWarStatus(), CastleWarStatus::PEACE);
    EXPECT_FALSE(castle->IsUnderAttack());
    EXPECT_FALSE(castle->CanStartWar()); // 没有攻击者时不能开始战争
    
    // 添加攻击者后应该可以开始战争
    castle->AddAttacker("攻击行会", GetCurrentTime());
    EXPECT_TRUE(castle->CanStartWar());
    
    // 开始战争
    castle->StartWar();
    EXPECT_EQ(castle->GetWarStatus(), CastleWarStatus::ACTIVE);
    EXPECT_TRUE(castle->IsUnderAttack());
    
    // 停止战争
    castle->StopWar();
    EXPECT_EQ(castle->GetWarStatus(), CastleWarStatus::PEACE);
    EXPECT_FALSE(castle->IsUnderAttack());
}

// 测试配置保存和加载
TEST_F(CastleSystemTest, ConfigSaveAndLoad) {
    Castle* castle = CastleManager::GetInstance().FindCastle("沙巴克");
    ASSERT_NE(castle, nullptr);
    
    // 修改一些配置
    castle->IncomeGold(2000);
    castle->SetTechLevel(3);
    castle->SetPower(150);
    castle->AddAttacker("测试攻击者", GetCurrentTime());
    
    // 保存配置
    castle->Save();
    
    // 重新初始化城堡管理器
    CastleManager::GetInstance().Finalize();
    CastleManager::GetInstance().Initialize("test_castle");
    
    // 重新获取城堡
    castle = CastleManager::GetInstance().FindCastle("沙巴克");
    ASSERT_NE(castle, nullptr);
    
    // 验证配置是否正确加载
    EXPECT_EQ(castle->GetTotalGold(), 2000);
    EXPECT_EQ(castle->GetTechLevel(), 3);
    EXPECT_EQ(castle->GetPower(), 150);
    EXPECT_TRUE(castle->IsAttacker("测试攻击者"));
}

// 测试城堡管理器的全局功能
TEST_F(CastleSystemTest, CastleManagerGlobalFunctions) {
    CastleManager& manager = CastleManager::GetInstance();
    
    // 测试获取城堡信息
    std::vector<std::string> goldInfo;
    manager.GetCastleGoldInfo(goldInfo);
    EXPECT_GT(goldInfo.size(), 0);
    
    std::vector<std::string> nameList;
    manager.GetCastleNameList(nameList);
    EXPECT_GT(nameList.size(), 0);
    EXPECT_TRUE(std::find(nameList.begin(), nameList.end(), "沙巴克") != nameList.end());
    
    // 测试全局收入
    manager.IncomeGold(1000);
    
    // 运行管理器
    manager.Run();
    
    // 保存所有数据
    manager.Save();
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
