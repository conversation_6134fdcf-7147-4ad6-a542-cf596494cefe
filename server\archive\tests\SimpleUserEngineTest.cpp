// SimpleUserEngineTest.cpp - 简化的UserEngine测试
#include "UserEngine.h"
#include "MapManager.h"
#include "ItemManager.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>

using namespace MirServer;

int main() {
    try {
        std::cout << "=== Simple UserEngine Test ===" << std::endl;
        
        // 初始化日志系统
        Logger::SetLogFile("simple_userengine_test.log");
        Logger::SetLogLevel(LogLevel::LOG_DEBUG);
        Logger::EnableConsoleOutput(true);
        
        std::cout << "1. Creating managers..." << std::endl;
        
        // 创建管理器
        auto mapManager = std::make_shared<MapManager>();
        auto itemManager = std::make_shared<ItemManager>();
        auto userEngine = std::make_unique<UserEngine>();
        
        std::cout << "2. Initializing MapManager..." << std::endl;
        if (!mapManager->Initialize("./maps")) {
            std::cout << "   MapManager initialization failed" << std::endl;
            return 1;
        }
        
        std::cout << "3. Initializing ItemManager..." << std::endl;
        if (!itemManager->Initialize("./data")) {
            std::cout << "   ItemManager initialization failed" << std::endl;
            return 1;
        }
        
        std::cout << "4. Initializing UserEngine..." << std::endl;
        if (!userEngine->Initialize(mapManager, itemManager)) {
            std::cout << "   UserEngine initialization failed" << std::endl;
            return 1;
        }
        
        std::cout << "5. Testing basic UserEngine functions..." << std::endl;
        
        // 测试统计信息
        auto stats = userEngine->GetStatistics();
        std::cout << "   Initial stats - Players: " << stats.totalPlayers 
                  << ", Active: " << stats.activePlayers << std::endl;
        
        // 测试创建玩家
        std::cout << "6. Testing player creation..." << std::endl;
        HumDataInfo humData;
        humData.charName = "TestPlayer";
        humData.account = "testaccount";
        humData.job = JobType::WARRIOR;
        humData.gender = GenderType::MALE;
        humData.level = 1;
        humData.mapName = "0";
        humData.currentPos = Point(100, 100);
        humData.gold = 1000;
        
        auto player = userEngine->CreatePlayer(humData);
        if (!player) {
            std::cout << "   Player creation failed" << std::endl;
            return 1;
        }
        
        std::cout << "   Player created: " << player->GetCharName() << std::endl;
        
        // 测试添加玩家
        std::cout << "7. Testing add player..." << std::endl;
        if (!userEngine->AddPlayer(player)) {
            std::cout << "   Add player failed" << std::endl;
            return 1;
        }
        
        // 检查统计信息
        stats = userEngine->GetStatistics();
        std::cout << "   After adding - Players: " << stats.totalPlayers 
                  << ", Active: " << stats.activePlayers << std::endl;
        
        // 测试查找玩家
        std::cout << "8. Testing find player..." << std::endl;
        auto foundPlayer = userEngine->GetPlayer("TestPlayer");
        if (!foundPlayer) {
            std::cout << "   Find player failed" << std::endl;
            return 1;
        }
        
        std::cout << "   Found player: " << foundPlayer->GetCharName() << std::endl;
        
        // 测试运行一次
        std::cout << "9. Testing UserEngine run..." << std::endl;
        userEngine->Run();
        std::cout << "   UserEngine run completed" << std::endl;
        
        // 测试移除玩家
        std::cout << "10. Testing remove player..." << std::endl;
        if (!userEngine->PlayerLogout("TestPlayer")) {
            std::cout << "   Remove player failed" << std::endl;
            return 1;
        }
        
        // 最终统计
        stats = userEngine->GetStatistics();
        std::cout << "   Final stats - Players: " << stats.totalPlayers 
                  << ", Active: " << stats.activePlayers << std::endl;
        
        std::cout << "11. Finalizing..." << std::endl;
        userEngine->Finalize();
        
        std::cout << "\n=== All tests passed! ===" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
}
