// GameEngine性能测试
#include "GameEngine.h"
#include "StorageManager.h"
#include "TradeManager.h"
#include "QuestManager.h"
#include "MiniMapManager.h"
#include "RepairManager.h"
#include "Environment.h"
#include "MapManager.h"
#include "ItemManager.h"
#include "MagicManager.h"
#include "NPCManager.h"
#include "MonsterManager.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>
#include <chrono>
#include <vector>
#include <thread>
#include <random>
#include <atomic>

using namespace MirServer;
using namespace std::chrono;

// 性能测试结果
struct PerformanceResult {
    std::string testName;
    double averageTime;
    double minTime;
    double maxTime;
    int iterations;
    bool success;

    PerformanceResult(const std::string& name) : testName(name), averageTime(0), minTime(0), maxTime(0), iterations(0), success(false) {}
};

// 性能测试统计
class PerformanceStats {
private:
    std::vector<PerformanceResult> results;

public:
    void AddResult(const PerformanceResult& result) {
        results.push_back(result);

        if (result.success) {
            Logger::Info("✓ " + result.testName + " - AVG: " + std::to_string(result.averageTime) + "ms");
        } else {
            Logger::Error("✗ " + result.testName + " - FAILED");
        }
    }

    void PrintSummary() {
        Logger::Info("=== Performance Test Summary ===");

        double totalAvgTime = 0;
        int successCount = 0;

        for (const auto& result : results) {
            if (result.success) {
                Logger::Info(result.testName + ":");
                Logger::Info("  Average: " + std::to_string(result.averageTime) + "ms");
                Logger::Info("  Min: " + std::to_string(result.minTime) + "ms");
                Logger::Info("  Max: " + std::to_string(result.maxTime) + "ms");
                Logger::Info("  Iterations: " + std::to_string(result.iterations));

                totalAvgTime += result.averageTime;
                successCount++;
            }
        }

        if (successCount > 0) {
            Logger::Info("Overall Average Time: " + std::to_string(totalAvgTime / successCount) + "ms");
        }

        Logger::Info("Successful Tests: " + std::to_string(successCount) + "/" + std::to_string(results.size()));
    }
};

// 计时器类
class PerfTimer {
private:
    high_resolution_clock::time_point startTime;

public:
    void Start() {
        startTime = high_resolution_clock::now();
    }

    double ElapsedMs() {
        auto endTime = high_resolution_clock::now();
        auto duration = duration_cast<microseconds>(endTime - startTime);
        return duration.count() / 1000.0;
    }
};

// 创建测试玩家
std::unique_ptr<PlayObject> CreateTestPlayer(const std::string& name, int level = 10) {
    auto player = std::make_unique<PlayObject>();
    player->SetCharName(name);
    player->SetLevel(level);
    player->SetJob(JobType::WARRIOR);
    player->IncGold(10000);
    return player;
}

// 测试GameEngine初始化性能
PerformanceResult TestGameEngineInitPerformance() {
    PerformanceResult result("GameEngine Initialization Performance");

    try {
        PerfTimer timer;
        timer.Start();

        auto& engine = GameEngine::GetInstance();
        bool initialized = engine.Initialize();

        double elapsedTime = timer.ElapsedMs();

        if (initialized) {
            result.averageTime = elapsedTime;
            result.minTime = elapsedTime;
            result.maxTime = elapsedTime;
            result.iterations = 1;
            result.success = true;
        }

    } catch (const std::exception& e) {
        Logger::Error("Exception in TestGameEngineInitPerformance: " + std::string(e.what()));
    }

    return result;
}

// 测试Environment创建性能
PerformanceResult TestEnvironmentCreationPerformance() {
    PerformanceResult result("Environment Creation Performance");

    try {
        const int iterations = 1000;
        std::vector<double> times;
        PerfTimer timer;

        for (int i = 0; i < iterations; i++) {
            timer.Start();

            Environment env("TestMap" + std::to_string(i), 100, 100);
            env.SetMapDesc("Test Map " + std::to_string(i));

            double elapsed = timer.ElapsedMs();
            times.push_back(elapsed);
        }

        // 计算统计信息
        double total = 0;
        double minTime = times[0];
        double maxTime = times[0];

        for (double time : times) {
            total += time;
            if (time < minTime) minTime = time;
            if (time > maxTime) maxTime = time;
        }

        result.averageTime = total / iterations;
        result.minTime = minTime;
        result.maxTime = maxTime;
        result.iterations = iterations;
        result.success = true;

    } catch (const std::exception& e) {
        Logger::Error("Exception in TestEnvironmentCreationPerformance: " + std::string(e.what()));
    }

    return result;
}

// 测试Storage操作性能
PerformanceResult TestStorageOperationPerformance() {
    PerformanceResult result("Storage Operation Performance");

    try {
        auto& engine = GameEngine::GetInstance();
        auto* storageManager = engine.GetStorageManager();

        if (!storageManager) {
            Logger::Error("StorageManager not available");
            return result;
        }

        const int iterations = 100;
        std::vector<double> times;
        PerfTimer timer;

        for (int i = 0; i < iterations; i++) {
            auto player = CreateTestPlayer("StoragePerfTest" + std::to_string(i));

            timer.Start();

            // 执行一系列仓库操作
            storageManager->OpenStorage(player.get(), "123456");
            storageManager->StoreGold(player.get(), 1000);
            storageManager->GetStorageGold(player.get());
            storageManager->CloseStorage(player.get());

            double elapsed = timer.ElapsedMs();
            times.push_back(elapsed);
        }

        // 计算统计信息
        double total = 0;
        double minTime = times[0];
        double maxTime = times[0];

        for (double time : times) {
            total += time;
            if (time < minTime) minTime = time;
            if (time > maxTime) maxTime = time;
        }

        result.averageTime = total / iterations;
        result.minTime = minTime;
        result.maxTime = maxTime;
        result.iterations = iterations;
        result.success = true;

    } catch (const std::exception& e) {
        Logger::Error("Exception in TestStorageOperationPerformance: " + std::string(e.what()));
    }

    return result;
}

// 测试并发性能
PerformanceResult TestConcurrentPerformance() {
    PerformanceResult result("Concurrent Operation Performance");

    try {
        auto& engine = GameEngine::GetInstance();
        auto* storageManager = engine.GetStorageManager();

        if (!storageManager) {
            Logger::Error("StorageManager not available");
            return result;
        }

        const int threadCount = 4;
        const int operationsPerThread = 25;
        std::vector<std::thread> threads;
        std::atomic<double> totalTime(0);
        std::atomic<int> completedOperations(0);

        PerfTimer globalTimer;
        globalTimer.Start();

        for (int t = 0; t < threadCount; t++) {
            threads.emplace_back([&, t]() {
                PerfTimer threadTimer;
                threadTimer.Start();

                for (int i = 0; i < operationsPerThread; i++) {
                    auto player = CreateTestPlayer("ConcurrentTest" + std::to_string(t) + "_" + std::to_string(i));

                    storageManager->OpenStorage(player.get(), "123456");
                    storageManager->StoreGold(player.get(), 500);
                    storageManager->CloseStorage(player.get());

                    completedOperations++;
                }

                double threadTime = threadTimer.ElapsedMs();
                double currentTotal = totalTime.load();
                while (!totalTime.compare_exchange_weak(currentTotal, currentTotal + threadTime)) {
                    // 重试直到成功
                }
            });
        }

        // 等待所有线程完成
        for (auto& thread : threads) {
            thread.join();
        }

        double globalElapsed = globalTimer.ElapsedMs();

        result.averageTime = totalTime / threadCount;
        result.minTime = globalElapsed;
        result.maxTime = globalElapsed;
        result.iterations = threadCount * operationsPerThread;
        result.success = (completedOperations == threadCount * operationsPerThread);

    } catch (const std::exception& e) {
        Logger::Error("Exception in TestConcurrentPerformance: " + std::string(e.what()));
    }

    return result;
}

// 测试内存使用性能
PerformanceResult TestMemoryUsagePerformance() {
    PerformanceResult result("Memory Usage Performance");

    try {
        PerfTimer timer;
        timer.Start();

        // 创建大量对象来测试内存管理
        std::vector<std::unique_ptr<PlayObject>> players;
        std::vector<std::unique_ptr<Environment>> environments;

        for (int i = 0; i < 1000; i++) {
            players.push_back(CreateTestPlayer("MemTest" + std::to_string(i)));
            environments.push_back(std::make_unique<Environment>("MemTestMap" + std::to_string(i), 50, 50));
        }

        // 清理对象
        players.clear();
        environments.clear();

        double elapsed = timer.ElapsedMs();

        result.averageTime = elapsed;
        result.minTime = elapsed;
        result.maxTime = elapsed;
        result.iterations = 1000;
        result.success = true;

    } catch (const std::exception& e) {
        Logger::Error("Exception in TestMemoryUsagePerformance: " + std::string(e.what()));
    }

    return result;
}

// 主测试函数
int main() {
    Logger::Info("=== GameEngine Performance Tests ===");

    PerformanceStats stats;

    try {
        // 运行性能测试
        stats.AddResult(TestGameEngineInitPerformance());
        stats.AddResult(TestEnvironmentCreationPerformance());
        stats.AddResult(TestStorageOperationPerformance());
        stats.AddResult(TestConcurrentPerformance());
        stats.AddResult(TestMemoryUsagePerformance());

        // 打印性能测试结果
        stats.PrintSummary();

        // 清理
        auto& engine = GameEngine::GetInstance();
        engine.Finalize();

        Logger::Info("=== Performance Tests Completed ===");
        return 0;

    } catch (const std::exception& e) {
        Logger::Error("Performance test suite failed with exception: " + std::string(e.what()));
        return 1;
    }
}
