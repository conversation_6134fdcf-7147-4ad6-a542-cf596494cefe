// CastleSystemExample.cpp - 城堡系统示例程序
#include "CastleManager.h"
#include "GuildManager.h"
#include "../Common/Logger.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace MirServer;

// 辅助函数：同时输出到控制台和日志
void LogAndPrint(const std::string& message) {
    std::cout << message << std::endl;
    Logger::Info("[DEMO] " + message);
}

void PrintCastleInfo(Castle* castle) {
    if (!castle) {
        LogAndPrint("城堡不存在！");
        return;
    }

    LogAndPrint("=== 城堡信息 ===");
    LogAndPrint("城堡名称: " + castle->GetCastleName());
    LogAndPrint("拥有者行会: " + (castle->GetOwnerGuild().empty() ? "无" : castle->GetOwnerGuild()));
    LogAndPrint("地图名称: " + castle->GetMapName());
    LogAndPrint("回城坐标: (" + std::to_string(castle->GetHomeX()) + ", " + std::to_string(castle->GetHomeY()) + ")");

    std::string warStatus;
    switch (castle->GetWarStatus()) {
        case CastleWarStatus::PEACE: warStatus = "和平"; break;
        case CastleWarStatus::PREPARING: warStatus = "准备中"; break;
        case CastleWarStatus::ACTIVE: warStatus = "战争中"; break;
        case CastleWarStatus::ENDING: warStatus = "结束中"; break;
    }
    LogAndPrint("战争状态: " + warStatus);
    LogAndPrint("是否被攻击: " + std::string(castle->IsUnderAttack() ? "是" : "否"));
    LogAndPrint("总金币: " + std::to_string(castle->GetTotalGold()));
    LogAndPrint("今日收入: " + std::to_string(castle->GetTodayIncome()));
    LogAndPrint("技术等级: " + std::to_string(castle->GetTechLevel()));
    LogAndPrint("力量值: " + std::to_string(castle->GetPower()));
    LogAndPrint("攻击者列表: " + castle->GetAttackerList());
    LogAndPrint("================");
    LogAndPrint("");
}

void DemonstrateCastleBasics() {
    LogAndPrint("\n=== 城堡基础功能演示 ===");

    CastleManager& manager = CastleManager::GetInstance();
    Castle* castle = manager.FindCastle("Sabuk");

    if (!castle) {
        LogAndPrint("未找到沙巴克城堡！");
        return;
    }

    LogAndPrint("1. 初始城堡状态:");
    PrintCastleInfo(castle);

    LogAndPrint("2. 添加收入:");
    castle->IncomeGold(5000);
    LogAndPrint("添加了5000金币收入");
    PrintCastleInfo(castle);

    LogAndPrint("3. 设置技术等级和力量:");
    castle->SetTechLevel(3);
    castle->SetPower(200);
    LogAndPrint("设置技术等级为3，力量值为200");
    PrintCastleInfo(castle);
}

void DemonstrateAttackerManagement() {
    LogAndPrint("\n=== 攻击者管理演示 ===");

    CastleManager& manager = CastleManager::GetInstance();
    Castle* castle = manager.FindCastle("Sabuk");

    if (!castle) {
        LogAndPrint("未找到Sabuk城堡！");
        return;
    }

    MirServer::DWORD currentTime = MirServer::GetCurrentTime();

    LogAndPrint("1. 添加攻击者:");
    castle->AddAttacker("霸者行会", currentTime);
    castle->AddAttacker("战神行会", currentTime);
    castle->AddAttacker("龙族行会", currentTime);
    LogAndPrint("添加了3个攻击者行会");
    PrintCastleInfo(castle);

    LogAndPrint("2. 检查攻击者:");
    LogAndPrint("霸者行会是否为攻击者: " + std::string(castle->IsAttacker("霸者行会") ? "是" : "否"));
    LogAndPrint("和平行会是否为攻击者: " + std::string(castle->IsAttacker("和平行会") ? "是" : "否"));

    LogAndPrint("3. 移除攻击者:");
    castle->RemoveAttacker("战神行会");
    LogAndPrint("移除了战神行会");
    PrintCastleInfo(castle);
}

void DemonstrateWarSystem() {
    LogAndPrint("\n=== 战争系统演示 ===");

    CastleManager& manager = CastleManager::GetInstance();
    Castle* castle = manager.FindCastle("Sabuk");

    if (!castle) {
        LogAndPrint("未找到Sabuk城堡！");
        return;
    }

    LogAndPrint("1. 检查是否可以开始战争:");
    LogAndPrint("可以开始战争: " + std::string(castle->CanStartWar() ? "是" : "否"));

    if (castle->CanStartWar()) {
        LogAndPrint("2. 开始城堡战争:");
        castle->StartWar();
        LogAndPrint("城堡战争已开始！");
        PrintCastleInfo(castle);

        LogAndPrint("3. 模拟战争进行中...");
        std::this_thread::sleep_for(std::chrono::seconds(2));

        LogAndPrint("4. 停止城堡战争:");
        castle->StopWar();
        LogAndPrint("城堡战争已停止！");
        PrintCastleInfo(castle);
    }
}

void DemonstrateDefenseSystem() {
    LogAndPrint("\n=== 防御系统演示 ===");

    CastleManager& manager = CastleManager::GetInstance();
    Castle* castle = manager.FindCastle("Sabuk");

    if (!castle) {
        LogAndPrint("未找到Sabuk城堡！");
        return;
    }

    LogAndPrint("1. 修复城门:");
    bool result = castle->RepairDoor();
    LogAndPrint("修复城门结果: " + std::string(result ? "成功" : "失败"));

    LogAndPrint("2. 修复城墙:");
    result = castle->RepairWall(0); // 左墙
    LogAndPrint("修复左墙结果: " + std::string(result ? "成功" : "失败"));
    result = castle->RepairWall(1); // 中墙
    LogAndPrint("修复中墙结果: " + std::string(result ? "成功" : "失败"));
    result = castle->RepairWall(2); // 右墙
    LogAndPrint("修复右墙结果: " + std::string(result ? "成功" : "失败"));

    LogAndPrint("3. 控制城门:");
    castle->MainDoorControl(false); // 关闭城门
    LogAndPrint("城门已关闭");
    castle->MainDoorControl(true);  // 打开城门
    LogAndPrint("城门已打开");
}

void DemonstrateCastleManager() {
    LogAndPrint("\n=== 城堡管理器演示 ===");

    CastleManager& manager = CastleManager::GetInstance();

    LogAndPrint("1. 城堡数量: " + std::to_string(manager.GetCastleCount()));

    LogAndPrint("2. 城堡名称列表:");
    std::vector<std::string> nameList;
    manager.GetCastleNameList(nameList);
    for (const auto& name : nameList) {
        LogAndPrint("  - " + name);
    }

    LogAndPrint("3. 城堡金币信息:");
    std::vector<std::string> goldInfo;
    manager.GetCastleGoldInfo(goldInfo);
    for (const auto& info : goldInfo) {
        LogAndPrint("  " + info);
    }

    LogAndPrint("4. 全局收入分配:");
    manager.IncomeGold(10000);
    LogAndPrint("分配了10000金币给所有城堡");

    manager.GetCastleGoldInfo(goldInfo);
    for (const auto& info : goldInfo) {
        LogAndPrint("  " + info);
    }
}

void DemonstrateSaveAndLoad() {
    LogAndPrint("\n=== 保存和加载演示 ===");

    CastleManager& manager = CastleManager::GetInstance();
    Castle* castle = manager.FindCastle("Sabuk");

    if (!castle) {
        LogAndPrint("未找到Sabuk城堡！");
        return;
    }

    LogAndPrint("1. 修改城堡数据:");
    castle->IncomeGold(8888);
    castle->SetTechLevel(5);
    castle->SetPower(500);
    castle->AddAttacker("测试行会", MirServer::GetCurrentTime());
    LogAndPrint("修改了城堡的金币、技术等级、力量值和攻击者");
    PrintCastleInfo(castle);

    LogAndPrint("2. 保存城堡数据:");
    castle->Save();
    manager.Save();
    LogAndPrint("城堡数据已保存");

    LogAndPrint("3. 运行城堡管理器:");
    manager.Run();
    LogAndPrint("城堡管理器运行完成");
}

int main() {
    LogAndPrint("城堡系统示例程序");
    LogAndPrint("==================");

    // 初始化日志系统
    Logger::SetLogFile("castle_example.log");
    Logger::SetLogLevel(LogLevel::LOG_INFO);

    try {
        // 初始化行会管理器（城堡系统依赖）
        LogAndPrint("初始化行会管理器...");
        GuildManager::GetInstance().Initialize();

        // 初始化城堡管理器
        LogAndPrint("初始化城堡管理器...");
        CastleManager::GetInstance().Initialize();

        // 演示各种功能
        DemonstrateCastleBasics();
        DemonstrateAttackerManagement();
        DemonstrateWarSystem();
        DemonstrateDefenseSystem();
        DemonstrateCastleManager();
        DemonstrateSaveAndLoad();

        LogAndPrint("\n=== 演示完成 ===");
        LogAndPrint("所有城堡系统功能演示完成！");

        // 清理
        CastleManager::GetInstance().Finalize();
        GuildManager::GetInstance().Finalize();

    } catch (const std::exception& e) {
        std::string errorMsg = "发生异常: " + std::string(e.what());
        std::cerr << errorMsg << std::endl;
        Logger::Error(errorMsg);
        return 1;
    }

    LogAndPrint("\n按任意键退出...");
    std::cin.get();

    return 0;
}
