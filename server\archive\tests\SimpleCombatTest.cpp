// SimpleCombatTest.cpp - 简化的战斗系统测试
#include "../Common/Types.h"
#include "../Protocol/PacketTypes.h"
#include <iostream>
#include <memory>
#include <cassert>
#include <cstdlib>
#include <ctime>

namespace MirServer {

// 简化的BaseObject用于测试
class SimpleBaseObject {
public:
    SimpleBaseObject(const std::string& name) : m_sCharName(name) {
        m_btRaceServer = RC_PLAYOBJECT;

        // 初始化基础属性
        m_WAbil.Level = 30;
        m_WAbil.HP = 1000;
        m_WAbil.MaxHP = 1000;
        m_WAbil.MP = 500;
        m_WAbil.MaxMP = 500;
        m_WAbil.DC.min = 50;
        m_WAbil.DC.max = 80;
        m_WAbil.AC.min = 10;
        m_WAbil.AC.max = 20;
        m_WAbil.MAC.min = 5;
        m_WAbil.MAC.max = 15;

        m_btHitPoint = 15;
        m_btSpeedPoint = 10;

        // 初始化装备
        m_useItems[U_WEAPON].wIndex = 1001;
        m_useItems[U_WEAPON].Dura = 5000;
        m_useItems[U_WEAPON].DuraMax = 5000;

        m_useItems[U_DRESS].wIndex = 2001;
        m_useItems[U_DRESS].Dura = 3000;
        m_useItems[U_DRESS].DuraMax = 3000;
    }

    virtual ~SimpleBaseObject() = default;

    // 基本属性访问
    const std::string& GetCharName() const { return m_sCharName; }
    bool IsDead() const { return m_WAbil.HP <= 0; }
    WORD GetHP() const { return m_WAbil.HP; }

    // 战斗系统方法
    bool IsProperTarget(const SimpleBaseObject* target) const {
        return target && target != this && !target->IsDead();
    }

    // 物理伤害计算
    int GetHitStruckDamage(SimpleBaseObject* attacker, int damage) {
        int defense = m_WAbil.AC.min + (rand() % (m_WAbil.AC.max - m_WAbil.AC.min + 1));
        damage = std::max(0, damage - defense);

        if (m_lifeAttrib == LA_UNDEAD && attacker) {
            // 对不死系额外伤害
        }

        if (damage > 0 && m_boAbilMagBubbleDefence) {
            damage = static_cast<int>((damage / 100.0) * (m_btMagBubbleDefenceLevel + 2) * 8.0);
        }

        return damage;
    }

    // 魔法伤害计算
    int GetMagStruckDamage(SimpleBaseObject* attacker, int damage) {
        int magDefense = m_WAbil.MAC.min + (rand() % (m_WAbil.MAC.max - m_WAbil.MAC.min + 1));
        damage = std::max(0, damage - magDefense);

        if (m_lifeAttrib == LA_UNDEAD && attacker) {
            // 对不死系额外魔法伤害
        }

        if (damage > 0 && m_boAbilMagBubbleDefence) {
            damage = static_cast<int>((damage / 100.0) * (m_btMagBubbleDefenceLevel + 2) * 8.0);
        }

        return damage;
    }

    // 受到伤害处理
    void StruckDamage(int damage) {
        if (damage <= 0) return;

        int weaponDamage = (rand() % 10) + 5;

        if (m_wStatusTimeArr[POISON_DAMAGEARMOR] > 0) {
            weaponDamage = static_cast<int>(weaponDamage * 1.2);
            damage = static_cast<int>(damage * 1.2);
        }

        // 损坏装备
        if (m_useItems[U_DRESS].wIndex > 0) {
            m_useItems[U_DRESS].Dura -= weaponDamage;
            if (m_useItems[U_DRESS].Dura <= 0) {
                m_useItems[U_DRESS].wIndex = 0;
                m_useItems[U_DRESS].Dura = 0;
            }
        }

        // 扣除生命值
        DamageHealth(damage);
    }

    // 攻击力计算
    int GetAttackPower(int basePower, int powerRange) {
        if (powerRange <= 0) return basePower;
        return basePower + (rand() % powerRange);
    }

    // 扣除生命值
    void DamageHealth(int damage) {
        if (damage <= 0) return;
        m_WAbil.HP = std::max(0, m_WAbil.HP - damage);
    }

    // 主攻击方法
    bool Attack(SimpleBaseObject* target) {
        if (!target || !IsProperTarget(target)) return false;

        // 计算基础攻击力
        int power = GetAttackPower(m_WAbil.DC.min, m_WAbil.DC.max - m_WAbil.DC.min);

        // 命中率检查
        if ((rand() % target->m_btSpeedPoint) >= m_btHitPoint) {
            return false; // 未命中
        }

        // 计算最终伤害
        power = target->GetHitStruckDamage(this, power);

        // 应用伤害
        if (power > 0) {
            target->StruckDamage(power);

            // 麻痹效果
            if (!target->m_boUnParalysis && m_boParalysis &&
                (rand() % (target->m_btAntiPoison + 5)) == 0) {
                target->MakePosion(POISON_STONE, 5, 0);
            }

            return true;
        }

        return false;
    }

    // 中毒状态
    bool MakePosion(int type, int time, int point) {
        if (type >= MAX_STATUS_ATTRIBUTE) return false;

        if (m_wStatusTimeArr[type] > 0) {
            if (m_wStatusTimeArr[type] < time) {
                m_wStatusTimeArr[type] = time;
            }
        } else {
            m_wStatusTimeArr[type] = time;
        }

        m_btGreenPoisoningPoint = point;
        return true;
    }

public:
    // 成员变量
    std::string m_sCharName;
    BYTE m_btRaceServer = 0;
    BYTE m_btHitPoint = 10;
    BYTE m_btSpeedPoint = 10;
    BYTE m_btAntiPoison = 0;
    BYTE m_lifeAttrib = 0;
    BYTE m_btGreenPoisoningPoint = 0;

    WORD m_wStatusTimeArr[MAX_STATUS_ATTRIBUTE] = {0};
    TUserItem m_useItems[U_MAXUSEITEM];
    TAbility m_WAbil;

    bool m_boParalysis = false;
    bool m_boUnParalysis = false;
    bool m_boAbilMagBubbleDefence = false;
    BYTE m_btMagBubbleDefenceLevel = 0;
};

class SimpleCombatTest {
public:
    static void RunAllTests() {
        std::cout << "=== Simple Combat System Test Started ===" << std::endl;

        srand(static_cast<unsigned int>(time(nullptr)));

        TestBasicDamageCalculation();
        TestPhysicalAttack();
        TestMagicalAttack();
        TestEquipmentDamage();
        TestPoisonSystem();
        TestCombatFlow();

        std::cout << "=== Simple Combat System Test Completed ===" << std::endl;
    }

private:
    static void TestBasicDamageCalculation() {
        std::cout << "\n--- Test Basic Damage Calculation ---" << std::endl;

        auto attacker = std::make_unique<SimpleBaseObject>("Attacker");
        auto target = std::make_unique<SimpleBaseObject>("Target");

        int damage = 100;
        int finalDamage = target->GetHitStruckDamage(attacker.get(), damage);

        std::cout << "Original damage: " << damage << ", Final damage: " << finalDamage << std::endl;
        assert(finalDamage >= 0 && finalDamage <= damage);

        int magDamage = 80;
        int finalMagDamage = target->GetMagStruckDamage(attacker.get(), magDamage);

        std::cout << "Original magic damage: " << magDamage << ", Final magic damage: " << finalMagDamage << std::endl;
        assert(finalMagDamage >= 0 && finalMagDamage <= magDamage);

        std::cout << "Basic damage calculation test passed" << std::endl;
    }

    static void TestPhysicalAttack() {
        std::cout << "\n--- Test Physical Attack ---" << std::endl;

        auto attacker = std::make_unique<SimpleBaseObject>("Warrior");
        auto target = std::make_unique<SimpleBaseObject>("Monster");

        WORD originalHP = target->GetHP();

        bool attackResult = attacker->Attack(target.get());

        std::cout << "Attack result: " << (attackResult ? "Hit" : "Miss") << std::endl;
        std::cout << "Target HP: " << originalHP << " -> " << target->GetHP() << std::endl;

        if (attackResult) {
            assert(target->GetHP() < originalHP);
        }

        std::cout << "Physical attack test passed" << std::endl;
    }

    static void TestMagicalAttack() {
        std::cout << "\n--- Test Magical Attack ---" << std::endl;

        auto mage = std::make_unique<SimpleBaseObject>("Mage");
        auto target = std::make_unique<SimpleBaseObject>("Target");

        WORD originalHP = target->GetHP();
        int magicDamage = 60;

        int finalDamage = target->GetMagStruckDamage(mage.get(), magicDamage);
        target->StruckDamage(finalDamage);

        std::cout << "Magic damage: " << magicDamage << " -> " << finalDamage << std::endl;
        std::cout << "Target HP: " << originalHP << " -> " << target->GetHP() << std::endl;

        assert(target->GetHP() < originalHP);
        std::cout << "Magical attack test passed" << std::endl;
    }

    static void TestEquipmentDamage() {
        std::cout << "\n--- Test Equipment Damage ---" << std::endl;

        auto target = std::make_unique<SimpleBaseObject>("Tester");

        WORD originalDura = target->m_useItems[U_DRESS].Dura;
        std::cout << "Original durability: " << originalDura << std::endl;

        for (int i = 0; i < 10; ++i) {
            target->StruckDamage(50);
        }

        std::cout << "Durability after attacks: " << target->m_useItems[U_DRESS].Dura << std::endl;
        assert(target->m_useItems[U_DRESS].Dura < originalDura);

        std::cout << "Equipment damage test passed" << std::endl;
    }

    static void TestPoisonSystem() {
        std::cout << "\n--- Test Poison System ---" << std::endl;

        auto target = std::make_unique<SimpleBaseObject>("Poisoned");

        bool poisonResult = target->MakePosion(POISON_GREEN, 10, 5);
        std::cout << "Poison result: " << (poisonResult ? "Success" : "Failed") << std::endl;

        assert(poisonResult);
        assert(target->m_wStatusTimeArr[POISON_GREEN] > 0);

        std::cout << "Poison system test passed" << std::endl;
    }

    static void TestCombatFlow() {
        std::cout << "\n--- Test Complete Combat Flow ---" << std::endl;

        auto player1 = std::make_unique<SimpleBaseObject>("Player1");
        auto player2 = std::make_unique<SimpleBaseObject>("Player2");

        std::cout << "Combat started:" << std::endl;
        std::cout << "Player1 HP: " << player1->GetHP() << std::endl;
        std::cout << "Player2 HP: " << player2->GetHP() << std::endl;

        for (int round = 1; round <= 5 && !player1->IsDead() && !player2->IsDead(); ++round) {
            std::cout << "\nRound " << round << ":" << std::endl;

            bool attack1 = player1->Attack(player2.get());
            std::cout << "Player1 attack: " << (attack1 ? "Hit" : "Miss") << ", Player2 HP: " << player2->GetHP() << std::endl;

            if (!player2->IsDead()) {
                bool attack2 = player2->Attack(player1.get());
                std::cout << "Player2 attack: " << (attack2 ? "Hit" : "Miss") << ", Player1 HP: " << player1->GetHP() << std::endl;
            }
        }

        std::cout << "\nCombat ended:" << std::endl;
        std::cout << "Player1 status: " << (player1->IsDead() ? "Dead" : "Alive") << " HP: " << player1->GetHP() << std::endl;
        std::cout << "Player2 status: " << (player2->IsDead() ? "Dead" : "Alive") << " HP: " << player2->GetHP() << std::endl;

        std::cout << "Complete combat flow test passed" << std::endl;
    }
};

} // namespace MirServer

// 主测试函数
int main() {
    try {
        MirServer::SimpleCombatTest::RunAllTests();
        std::cout << "\nAll tests passed! Combat system implementation is correct." << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed: " << e.what() << std::endl;
        return 1;
    }
}
